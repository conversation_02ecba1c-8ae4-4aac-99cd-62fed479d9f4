destilink:
  fw:
    core:
      logging:
        # Legacy message size limiting (still supported for backward compatibility)
        message-size-limit: 500KB
        
        # New structured logging configuration
        structured:
          enabled: true  # Enabled by default for Spring Boot 3.5+ native support
          message-size-limit: 500KB
          include-global-context: true
          format: "destilink"
          use-native-structured-logging: true

# Spring Boot 3.5+ native structured logging configuration
# These properties are used when structured logging is enabled
logging:
  structured:
    format:
      console: "com.tui.destilink.framework.core.logging.structured.DestilinkStructuredLogFormatter"
      # file: "ecs"  # Can use native formats for file logging
    ecs:
      service:
        name: "${spring.application.name:destilink-service}"
        version: "${destilink.fw.version:unknown}"
        environment: "${spring.profiles.active:development}"

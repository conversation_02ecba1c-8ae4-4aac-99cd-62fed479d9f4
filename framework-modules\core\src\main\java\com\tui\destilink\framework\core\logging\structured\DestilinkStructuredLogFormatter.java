package com.tui.destilink.framework.core.logging.structured;

import ch.qos.logback.classic.spi.ILoggingEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tui.destilink.framework.core.logging.LoggingUtils;
import com.tui.destilink.framework.core.logging.context.global.GlobalContextJsonProvider;
import com.tui.destilink.framework.core.logging.marker.ConstraintViolationMarker;
import net.logstash.logback.marker.LogstashMarker;
import org.slf4j.Marker;
import org.springframework.boot.logging.structured.StructuredLogFormatter;
import org.springframework.util.unit.DataSize;

import java.time.Instant;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Destilink Framework custom structured log formatter that preserves existing functionality
 * while leveraging Spring Boot 3.5+ native structured logging infrastructure.
 * 
 * <p>This formatter provides:
 * <ul>
 *   <li>Message size limiting with configurable thresholds</li>
 *   <li>Global context injection</li>
 *   <li>ECS-compatible field structure</li>
 *   <li>Unicode-aware truncation</li>
 * </ul>
 */
public class DestilinkStructuredLogFormatter implements StructuredLogFormatter<ILoggingEvent> {

    private final DataSize messageSizeLimit;
    private final ObjectMapper objectMapper;
    private final boolean includeGlobalContext;

    /**
     * Default constructor with sensible defaults for Spring Boot's StructuredLogFormatterFactory.
     * Uses default message size limit of 500KB and enables global context.
     */
    public DestilinkStructuredLogFormatter() {
        this(DataSize.ofKilobytes(500), true);
    }

    /**
     * Constructor with custom configuration.
     * @param messageSizeLimit the maximum size for log messages
     * @param includeGlobalContext whether to include global context in logs
     */
    public DestilinkStructuredLogFormatter(DataSize messageSizeLimit, boolean includeGlobalContext) {
        this.messageSizeLimit = messageSizeLimit;
        this.includeGlobalContext = includeGlobalContext;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public String format(ILoggingEvent event) {
        Map<String, Object> logData = new LinkedHashMap<>();
        
        // ECS Base fields
        logData.put("@timestamp", Instant.ofEpochMilli(event.getTimeStamp()).toString());
        logData.put("log.level", event.getLevel().toString());
        logData.put("process.pid", ProcessHandle.current().pid());
        logData.put("process.thread.name", event.getThreadName());
        logData.put("log.logger", event.getLoggerName());
        
        // Apply message size limiting (preserve existing Destilink functionality)
        String message = event.getFormattedMessage();
        if (message != null) {
            String limitedMessage = LoggingUtils.limitToBytes(message, messageSizeLimit);
            logData.put("message", limitedMessage);
        }
        
        // Include MDC properties
        Map<String, String> mdc = event.getMDCPropertyMap();
        if (mdc != null && !mdc.isEmpty()) {
            logData.putAll(mdc);
        }
        
        // Include Global Context (preserve existing Destilink functionality)
        if (includeGlobalContext) {
            Map<String, String> globalContext = GlobalContextJsonProvider.getGlobalContext();
            if (!globalContext.isEmpty()) {
                globalContext.forEach((key, value) -> {
                    // Only add if not already present (MDC takes precedence)
                    if (!logData.containsKey(key)) {
                        logData.put(key, value);
                    }
                });
            }
        }
        
        // Process Logstash markers to maintain backward compatibility
        processLogstashMarkers(event, logData);
        
        // Exception handling
        if (event.getThrowableProxy() != null) {
            logData.put("error.type", event.getThrowableProxy().getClassName());
            logData.put("error.message", event.getThrowableProxy().getMessage());
        }
        
        try {
            return objectMapper.writeValueAsString(logData);
        } catch (JsonProcessingException e) {
            // Fallback to basic structure
            return String.format("{\"@timestamp\":\"%s\",\"log.level\":\"%s\",\"message\":\"Failed to serialize log event: %s\"}",
                    Instant.ofEpochMilli(event.getTimeStamp()), event.getLevel(), e.getMessage());
        }
    }
    
    /**
     * Processes Logstash markers from the logging event and adds their field data
     * to the log data map to maintain backward compatibility with existing marker-based logging.
     *
     * @param event the logging event
     * @param logData the log data map to add marker fields to
     */
    private void processLogstashMarkers(ILoggingEvent event, Map<String, Object> logData) {
        Marker marker = event.getMarker();
        if (marker == null) {
            return;
        }
        
        processMarker(marker, logData);
    }
    
    /**
     * Recursively processes a marker and its references to extract field data.
     *
     * @param marker the marker to process
     * @param logData the log data map to add marker fields to
     */
    private void processMarker(Marker marker, Map<String, Object> logData) {
        if (marker instanceof LogstashMarker) {
            LogstashMarker logstashMarker = (LogstashMarker) marker;
            
            // Handle specific known marker types that we can access
            if (logstashMarker instanceof ConstraintViolationMarker) {
                ConstraintViolationMarker constraintMarker = (ConstraintViolationMarker) logstashMarker;
                Object fieldValue = constraintMarker.getFieldValue();
                String fieldName = ConstraintViolationMarker.FIELD_NAME;
                if (fieldValue != null) {
                    logData.put(fieldName, fieldValue);
                }
            }
            // Could add other specific marker types here as needed
        }
        
        // Process marker references recursively
        if (marker.hasReferences()) {
            Iterator<Marker> references = marker.iterator();
            while (references.hasNext()) {
                processMarker(references.next(), logData);
            }
        }
    }
}

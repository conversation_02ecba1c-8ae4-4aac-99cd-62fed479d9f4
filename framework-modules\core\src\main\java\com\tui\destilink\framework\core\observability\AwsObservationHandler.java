package com.tui.destilink.framework.core.observability;

import io.micrometer.common.KeyValue;
import io.micrometer.observation.Observation;
import io.micrometer.observation.ObservationHandler;

/**
 * Observation handler for AWS operations.
 * Adds AWS-specific metrics and tracing context.
 * 
 * @since Spring Boot 3.5.4
 */
public class AwsObservationHandler implements ObservationHandler<Observation.Context> {

    @Override
    public void onStart(Observation.Context context) {
        if (context.getName().startsWith("aws.")) {
            context.addLowCardinalityKeyValue(KeyValue.of("component", "aws"));
        }
    }

    @Override
    public void onError(Observation.Context context) {
        if (context.getName().startsWith("aws.")) {
            context.addLowCardinalityKeyValue(KeyValue.of("aws.error", "true"));
        }
    }

    @Override
    public void onEvent(Observation.Event event, Observation.Context context) {
        // Handle AWS-specific events
    }

    @Override
    public void onScopeOpened(Observation.Context context) {
        // AWS scope opened
    }

    @Override
    public void onScopeClosed(Observation.Context context) {
        // AWS scope closed
    }

    @Override
    public void onStop(Observation.Context context) {
        // AWS operation completed
    }

    @Override
    public boolean supportsContext(Observation.Context context) {
        return context.getName().startsWith("aws.");
    }
}

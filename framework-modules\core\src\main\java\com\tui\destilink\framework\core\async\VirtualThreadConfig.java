package com.tui.destilink.framework.core.async;

import org.springframework.boot.autoconfigure.condition.ConditionalOnJava;
import org.springframework.boot.system.JavaVersion;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.VirtualThreadTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Virtual Threads configuration for Spring Boot 3.5+ with Java 21+.
 * Provides Virtual Thread-based executors for improved scalability and resource utilization.
 * 
 * @since Spring Boot 3.5.4
 * @since Java 21
 */
@Configuration
@ConditionalOnJava(JavaVersion.TWENTY_ONE)
@EnableAsync
public class VirtualThreadConfig {

    /**
     * Primary async task executor using Virtual Threads.
     * Replaces traditional thread pools for async operations.
     */
    @Bean(name = "virtualThreadTaskExecutor")
    public AsyncTaskExecutor virtualThreadTaskExecutor() {
        VirtualThreadTaskExecutor executor = new VirtualThreadTaskExecutor("virtual-async-");
        return executor;
    }

    /**
     * Virtual Thread ExecutorService for direct use in framework components.
     * Ideal for high-concurrency, IO-bound operations like Redis connections.
     */
    @Bean(name = "virtualThreadExecutorService")
    public ExecutorService virtualThreadExecutorService() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * Named Virtual Thread ExecutorService for Redis operations.
     * Optimizes Redis connection pooling and async operations.
     */
    @Bean(name = "redisVirtualThreadExecutor")
    public ExecutorService redisVirtualThreadExecutor() {
        return Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("redis-virtual-", 0).factory()
        );
    }

    /**
     * Named Virtual Thread ExecutorService for AWS operations.
     * Optimizes AWS SDK async operations and connection management.
     */
    @Bean(name = "awsVirtualThreadExecutor")
    public ExecutorService awsVirtualThreadExecutor() {
        return Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("aws-virtual-", 0).factory()
        );
    }

    /**
     * Named Virtual Thread ExecutorService for web client operations.
     * Optimizes HTTP client connection pooling and async requests.
     */
    @Bean(name = "webClientVirtualThreadExecutor")
    public ExecutorService webClientVirtualThreadExecutor() {
        return Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("webclient-virtual-", 0).factory()
        );
    }
}

package com.tui.destilink.framework.redis.core.cluster.pool;

import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import org.apache.commons.pool2.impl.AbandonedConfig;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledOnJre;
import org.junit.jupiter.api.condition.JRE;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test suite for Virtual Thread pinning detection in VirtualThreadClusterConnectionPool.
 * 
 * These tests ensure that the Virtual Thread implementation does not cause carrier thread pinning,
 * which would negate the benefits of using Virtual Threads.
 * 
 * Key pinning scenarios tested:
 * 1. Connection acquisition under load
 * 2. Connection return operations  
 * 3. Pool initialization and cleanup
 * 4. Timeout handling
 * 5. Exception scenarios
 * 6. High concurrency operations
 * 
 * Uses -Djdk.tracePinnedThreads=full for pinning detection.
 */
@EnabledOnJre(JRE.JAVA_21)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class VirtualThreadPinningTest {

    private static final int HIGH_CONCURRENCY_THREAD_COUNT = 100;
    private static final int OPERATIONS_PER_THREAD = 50;
    private static final Duration TEST_TIMEOUT = Duration.ofSeconds(30);
    
    private RedisClusterClient mockRedisClient;
    private StatefulRedisClusterConnection<String, String> mockConnection;
    private VirtualThreadClusterConnectionPool connectionPool;
    private ExecutorService virtualThreadExecutor;
    
    // Capture System.err to detect pinning traces
    private ByteArrayOutputStream capturedOutput;
    private PrintStream originalSystemErr;

    @BeforeEach
    void setUp() {
        // Set up pinning detection - capture System.err for pinning traces
        capturedOutput = new ByteArrayOutputStream();
        originalSystemErr = System.err;
        System.setErr(new PrintStream(capturedOutput));
        
        // Mock Redis components
        mockRedisClient = mock(RedisClusterClient.class);
        @SuppressWarnings("unchecked")
        StatefulRedisClusterConnection<String, String> typedMockConnection = mock(StatefulRedisClusterConnection.class);
        mockConnection = typedMockConnection;
        
        // Mock connection creation to avoid real Redis dependency
        when(mockRedisClient.connect()).thenReturn(mockConnection);
        @SuppressWarnings("unchecked")
        io.lettuce.core.cluster.api.async.RedisAdvancedClusterAsyncCommands<String, String> mockAsync = 
            mock(io.lettuce.core.cluster.api.async.RedisAdvancedClusterAsyncCommands.class);
        when(mockConnection.async()).thenReturn(mockAsync);
        
        // Create Virtual Thread executor
        virtualThreadExecutor = Executors.newThreadPerTaskExecutor(
            Thread.ofVirtual().name("test-virtual-", 0).factory()
        );
        
        // Create test pool
        RedisProperties properties = new RedisProperties();
        properties.setClientName("test-client");
        
        GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> poolConfig = 
            new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(20);
        poolConfig.setMaxIdle(10);
        poolConfig.setMinIdle(2);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setBlockWhenExhausted(false); // Prevent blocking in tests
        
        AbandonedConfig abandonedConfig = new AbandonedConfig();
        abandonedConfig.setRemoveAbandonedOnBorrow(true);
        abandonedConfig.setRemoveAbandonedOnMaintenance(true);
        abandonedConfig.setRemoveAbandonedTimeout(Duration.ofSeconds(30));
        
        connectionPool = new VirtualThreadClusterConnectionPool(
            mockRedisClient, "test-pool", poolConfig, abandonedConfig, virtualThreadExecutor
        );
    }

    @AfterEach
    void tearDown() throws Exception {
        // Restore System.err
        System.setErr(originalSystemErr);
        
        // Clean up resources
        if (connectionPool != null) {
            connectionPool.destroy();
        }
        if (virtualThreadExecutor != null && !virtualThreadExecutor.isShutdown()) {
            virtualThreadExecutor.shutdown();
            virtualThreadExecutor.awaitTermination(5, TimeUnit.SECONDS);
        }
        
        // Check for pinning in captured output
        String output = capturedOutput.toString();
        assertNoPinningDetected(output);
    }

    @Test
    @Order(1)
    @DisplayName("Test no pinning during pool initialization")
    void testPoolInitializationNoPinning() throws Exception {
        // Initialize pool
        connectionPool.afterPropertiesSet();
        
        assertTrue(connectionPool.isInitialized(), "Pool should be initialized");
        
        // Give some time for any async operations to complete
        Thread.sleep(100);
        
        // Check output immediately for any pinning during initialization
        String output = capturedOutput.toString();
        assertNoPinningDetected(output);
    }

    @Test
    @Order(2)
    @DisplayName("Test no pinning during single connection acquisition")
    void testSingleConnectionAcquisitionNoPinning() throws Exception {
        connectionPool.afterPropertiesSet();
        
        // Acquire connection using Virtual Thread
        CompletableFuture<StatefulRedisClusterConnection<String, String>> future = 
            connectionPool.getConnection(Duration.ofSeconds(5));
        
        StatefulRedisClusterConnection<String, String> connection = 
            assertTimeoutPreemptively(TEST_TIMEOUT, () -> future.get());
        
        assertNotNull(connection, "Connection should be acquired");
        
        // Return connection
        CompletableFuture<Void> returnFuture = connectionPool.returnConnection(connection);
        assertTimeoutPreemptively(TEST_TIMEOUT, () -> returnFuture.get());
    }

    @Test
    @Order(3)
    @DisplayName("Test no pinning under high concurrency load")
    void testHighConcurrencyNoPinning() throws Exception {
        connectionPool.afterPropertiesSet();
        
        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger errorCount = new AtomicInteger();
        CountDownLatch latch = new CountDownLatch(HIGH_CONCURRENCY_THREAD_COUNT);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // Create many Virtual Threads performing connection operations
        for (int i = 0; i < HIGH_CONCURRENCY_THREAD_COUNT; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    for (int j = 0; j < OPERATIONS_PER_THREAD; j++) {
                        // Acquire connection
                        CompletableFuture<StatefulRedisClusterConnection<String, String>> connFuture = 
                            connectionPool.getConnection(Duration.ofMillis(100));
                        
                        try {
                            StatefulRedisClusterConnection<String, String> connection = 
                                connFuture.get(1, TimeUnit.SECONDS);
                            
                            // Simulate some work
                            Thread.sleep(1);
                            
                            // Return connection
                            connectionPool.returnConnection(connection).get(1, TimeUnit.SECONDS);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            }, virtualThreadExecutor);
            
            futures.add(future);
        }

        // Wait for all operations to complete
        assertTrue(latch.await(TEST_TIMEOUT.getSeconds(), TimeUnit.SECONDS), 
                  "All operations should complete within timeout");
        
        // Wait for futures to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .get(TEST_TIMEOUT.getSeconds(), TimeUnit.SECONDS);

        System.out.println("High concurrency test completed - Success: " + successCount.get() + 
                          ", Errors: " + errorCount.get());
        
        // Some operations may fail due to pool exhaustion, but no pinning should occur
        assertTrue(successCount.get() > 0, "At least some operations should succeed");
    }

    @Test
    @Order(4)
    @DisplayName("Test no pinning during timeout scenarios")
    void testTimeoutHandlingNoPinning() throws Exception {
        connectionPool.afterPropertiesSet();
        
        // Test very short timeout - should not cause pinning
        CompletableFuture<StatefulRedisClusterConnection<String, String>> future = 
            connectionPool.getConnection(Duration.ofMillis(1));
        
        try {
            future.get(2, TimeUnit.SECONDS);
        } catch (Exception e) {
            // Timeout expected - should not cause pinning
            assertTrue(e.getCause() instanceof TimeoutException || 
                      e instanceof java.util.concurrent.ExecutionException);
        }
    }

    @Test
    @Order(5)
    @DisplayName("Test no pinning during connection pool stress")
    void testConnectionPoolStressNoPinning() throws Exception {
        connectionPool.afterPropertiesSet();
        
        List<StatefulRedisClusterConnection<String, String>> connections = new ArrayList<>();
        
        // Exhaust the pool
        for (int i = 0; i < 25; i++) { // More than pool max
            try {
                CompletableFuture<StatefulRedisClusterConnection<String, String>> future = 
                    connectionPool.getConnection(Duration.ofMillis(50));
                StatefulRedisClusterConnection<String, String> connection = 
                    future.get(1, TimeUnit.SECONDS);
                connections.add(connection);
            } catch (Exception e) {
                // Pool exhaustion expected
                break;
            }
        }
        
        assertTrue(connections.size() > 0, "Should acquire some connections");
        
        // Return all connections
        for (StatefulRedisClusterConnection<String, String> connection : connections) {
            connectionPool.returnConnection(connection).get(1, TimeUnit.SECONDS);
        }
    }

    @Test
    @Order(6)
    @DisplayName("Test no pinning during rapid acquire/return cycles")
    void testRapidCyclesNoPinning() throws Exception {
        connectionPool.afterPropertiesSet();
        
        AtomicLong totalOperations = new AtomicLong();
        long startTime = System.currentTimeMillis();
        long testDuration = 5000; // 5 seconds
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // Start multiple Virtual Threads doing rapid acquire/return cycles
        for (int i = 0; i < 10; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                while (System.currentTimeMillis() - startTime < testDuration) {
                    try {
                        StatefulRedisClusterConnection<String, String> connection = 
                            connectionPool.getConnection(Duration.ofMillis(10)).get(100, TimeUnit.MILLISECONDS);
                        connectionPool.returnConnection(connection).get(100, TimeUnit.MILLISECONDS);
                        totalOperations.incrementAndGet();
                    } catch (Exception e) {
                        // Ignore timeouts and continue
                    }
                }
            }, virtualThreadExecutor);
            
            futures.add(future);
        }
        
        // Wait for test completion
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .get(testDuration + 2000, TimeUnit.MILLISECONDS);
        
        System.out.println("Rapid cycles test completed - Total operations: " + totalOperations.get());
        assertTrue(totalOperations.get() > 0, "Should complete some operations");
    }

    @Test
    @Order(7)
    @DisplayName("Test Virtual Thread count and behavior")
    void testVirtualThreadBehavior() throws Exception {
        connectionPool.afterPropertiesSet();
        
        // Test that we're actually using Virtual Threads
        AtomicInteger virtualThreadCount = new AtomicInteger();
        AtomicInteger platformThreadCount = new AtomicInteger();
        
        CountDownLatch latch = new CountDownLatch(20);
        
        for (int i = 0; i < 20; i++) {
            CompletableFuture.runAsync(() -> {
                Thread currentThread = Thread.currentThread();
                if (currentThread.isVirtual()) {
                    virtualThreadCount.incrementAndGet();
                } else {
                    platformThreadCount.incrementAndGet();
                }
                
                try {
                    // Perform connection operation
                    StatefulRedisClusterConnection<String, String> connection = 
                        connectionPool.getConnection(Duration.ofSeconds(1)).get(2, TimeUnit.SECONDS);
                    connectionPool.returnConnection(connection).get(2, TimeUnit.SECONDS);
                } catch (Exception e) {
                    // Ignore for this test
                } finally {
                    latch.countDown();
                }
            }, virtualThreadExecutor);
        }
        
        assertTrue(latch.await(10, TimeUnit.SECONDS), "All operations should complete");
        
        System.out.println("Virtual threads used: " + virtualThreadCount.get() + 
                          ", Platform threads used: " + platformThreadCount.get());
        
        // All operations should use Virtual Threads
        assertEquals(20, virtualThreadCount.get(), "All operations should use Virtual Threads");
        assertEquals(0, platformThreadCount.get(), "No platform threads should be used");
    }

    /**
     * Asserts that no Virtual Thread pinning was detected in the captured output.
     * Looks for telltale signs of pinning traces from -Djdk.tracePinnedThreads=full
     */
    private void assertNoPinningDetected(String output) {
        // Look for pinning indicators in JVM output
        String[] pinningIndicators = {
            "onPinned",
            "VirtualThread.park",
            "monitors:",
            "Thread[#",
            "CarrierThreads"
        };
        
        for (String indicator : pinningIndicators) {
            if (output.contains(indicator)) {
                fail("Virtual Thread pinning detected! Output contains: '" + indicator + 
                     "'\nFull output:\n" + output);
            }
        }
        
        // Also check for stack traces that indicate blocking in synchronized blocks
        if (output.contains("at java.base/java.lang.Object.wait(") ||
            output.contains("VirtualThread.parkNanos") ||
            output.contains("Continuation.onPinned")) {
            fail("Virtual Thread blocking/pinning detected in output:\n" + output);
        }
    }
}

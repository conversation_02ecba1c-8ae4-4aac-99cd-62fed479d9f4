# =======================================================
# Destilink Framework - Async Core Configuration
# Spring Boot 3.5+ Virtual Threads Support
# =======================================================

destilink:
  fw:
    async:
      core:
        # Enable async operations and Virtual Threads support
        enabled: true
        # Maximum number of threads for throttled executor (fallback)
        max-threads: 10
        # Whether to use fair scheduling for throttled executor
        fair: true
        # Enable Virtual Threads (requires Java 21+)
        virtual-threads-enabled: true
        # Thread name prefix for Virtual Threads
        virtual-thread-prefix: "virtual-async-"

# =======================================================
# Spring Boot Threading Configuration
# =======================================================
spring:
  threads:
    virtual:
      enabled: true

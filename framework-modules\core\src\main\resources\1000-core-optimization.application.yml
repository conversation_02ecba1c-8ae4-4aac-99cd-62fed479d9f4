# =======================================================
# Destilink Framework - Core Observability Configuration
# Spring Boot 3.5+ Enhanced Observability Support
# =======================================================

destilink:
  fw:
    core:
      observability:
        # Enable enhanced observability features
        enabled: true
        # Enable Redis operation observations
        redis-observation-enabled: true
        # Enable AWS operation observations
        aws-observation-enabled: true
        # Enable web client operation observations
        web-client-observation-enabled: true
        # Enable high cardinality tags (use with caution in production)
        high-cardinality-tags: false

# =======================================================
# Spring Boot Management/Actuator Observability
# =======================================================
management:
  # Enable observability features in Spring Boot 3.5+
  observability:
    enabled: true
  
  # Enhanced metrics configuration
  metrics:
    tags:
      framework: destilink
      java-version: "${java.version}"

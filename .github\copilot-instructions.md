# Destilink Framework AI Coding Assistant Guide

This document provides essential guidelines for AI-powered development within the Destilink Framework. Adhering to these instructions is critical for maintaining code quality, consistency, and stability.

## Quick Start for Immediate Productivity

**First Time Setup**:
```bash
# 1. Start development environment
docker-compose -f utils/docker-compose.yaml up -d

# 2. Build entire framework
mvn clean install

# 3. Run tests to verify setup
mvn test -Dtest="**/*IT" # Integration tests
```

**Most Common Commands**:
- **Full Build**: `mvn clean install`
- **Run Tests**: `mvn test` (unit) or `mvn test -Dtest="**/*IT"` (integration)  
- **Generate Documentation**: `mkdocs build`
- **View Dependencies**: `mvn dependency:tree -Dverbose`

**Critical Rules to Remember**:
1. **NEVER use `@ComponentScan`** - use explicit `@Import` only
2. **ALWAYS use test-support modules** - never `spring-boot-starter-test` directly
3. **Constructor injection only** - no `@Autowired` fields
4. **All `@Bean` methods need conditionals** - `@ConditionalOnMissingBean`, etc.

## Memory & Context Management

**MANDATORY**: Use MCP Server "mem0" tools for persistent memory across all coding tasks:

### Before Every Task
- **ALWAYS** use `search-memories` with user's query or relevant keywords:
  ```
  search-memories "Redis distributed locking" OR "Spring Boot auto-configuration"
  ```
- Search for technology/framework patterns, past solutions, and known issues
- Use results to inform your approach and avoid repeated mistakes

### After Task Completion  
- **ALWAYS** use `add-memory` to store:
  - Task summary and technical approach taken
  - Challenges encountered and solutions implemented
  - Code patterns that worked well
  - Context: project constraints, tech stack, user preferences

### Available Tools
- `search-memories` - Search existing knowledge before starting
- `add-memory` - Store new learnings and solutions  
- `list-memories` - Review stored knowledge (use sparingly)
- `delete-all-memories` - Complete reset (only when explicitly requested)

## Framework Architecture Overview

### Core Principles
- **Opinionated Spring Boot Abstraction**: Destilink standardizes and accelerates microservice and cronjob development
- **Multi-Module Maven Structure**: 15+ framework modules with hierarchical dependency management
- **Auto-Configuration First**: Custom Spring Boot auto-configuration pattern with explicit component registration
- **Test-Support Ecosystem**: Comprehensive test modules for all technologies (Redis, PostgreSQL, Keycloak, etc.)
- **Asynchronous-First Design**: Built on Java's `CompletableFuture` and Virtual Threads for non-blocking operations

### Project Structure
```
framework-dependencies-parent/     # Central version management
framework-bom/                    # Bill of Materials for all modules
framework-build-parent/           # Common build configuration
├── framework-build-parent-ms/    # Microservice build parent
└── framework-build-parent-cronjob/ # Cronjob build parent
framework-modules/                # Core implementation modules (15+)
├── core/                        # Logging, metrics, tracing foundations
├── ms-core/                     # Microservice-specific functionality
├── cronjob-core/               # Cronjob-specific functionality
├── web/                        # Web modules (core, server, security, openapi)
├── redis-core/                 # Redis integration
├── locking/                    # Distributed locking (Redis, ShedLock)
├── aws/                        # AWS service integrations
├── test-support/               # Test framework modules
└── [other modules]
framework-test-applications/      # Integration test applications
utils/                           # Docker Compose development environment
```

### Technology Stack
- **Java 21** with Virtual Threads for async operations
- **Spring Boot 3.4.5** with custom auto-configuration patterns
- **Maven** multi-module project with strict dependency management
- **Redis** for distributed locking and caching
- **Docker Compose** for local development (PostgreSQL, Redis, Keycloak, LocalStack)

### Key Architectural Components

#### Distributed Locking (`locking-redis-lock`)
- **Atomicity**: All Redis operations executed as atomic Lua scripts
- **Idempotency**: Operations managed by `RedisLockOperationsImpl` with `requestUuid`
- **Watchdog**: `LockWatchdog` service automatically extends active lock leases
- **Key Schema**: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`

#### Build & Test Commands

**Essential Development Commands**:
*   **Full Build**: `mvn clean install` 
*   **Run All Tests**: `mvn test`
*   **Run Single Test**: `mvn test -Dtest=com.example.TestClass`
*   **Run Test Pattern**: `mvn test -Dtest="**/Redis*Test"`
*   **Build Documentation**: `mkdocs build` (uses `mkdocs.yml`)
*   **Start Development Environment**: `docker-compose -f utils/docker-compose.yaml up -d`
*   **Generate Dependency Graph**: `mvn com.github.ferstl:depgraph-maven-plugin:aggregate` (output in `target/dependency-graphs/`)
*   **Verify Transitive Dependencies**: `mvn dependency:tree -Dverbose`

**Integration Testing Workflow**:
1. Start services: `docker-compose -f utils/docker-compose.yaml up -d`
2. Verify connectivity: Check Redis (port 6378), PostgreSQL (5432), Keycloak, LocalStack
3. Run integration tests: `mvn test -Dtest="**/*IT"`
4. Review test reports in `target/surefire-reports/`

**Redis-Specific Development Commands**:
*   **Redis CLI Access**: `docker exec -it destilink-redis-cluster-node-1 redis-cli -p 6379 -a cluster`
*   **Redis Insight**: Available at `http://localhost:5540` for debugging
*   **Check Redis ACL Users**: `redis-cli -p 6378 -a cluster ACL LIST`
*   **Test Redis Keyspace**: `redis-cli -p 6378 -a cluster KEYS "test-*"`
*   **Monitor Redis Commands**: `redis-cli -p 6378 -a cluster MONITOR`

**Debugging Redis Tests**:
*   **Test with ROOT User**: Use `RedisTestUtils.executeAsRoot()` for admin operations
*   **Check ACL Permissions**: Use `RedisTestUtils.checkUserPermissions()` for debugging
*   **Extract Key Formats**: Use debug utilities to log actual key composition
*   **Test Isolation**: Each test class gets unique Redis user and keyspace prefix

### Critical Coding Rules

### Component Registration Rules (MANDATORY)

**ZERO TOLERANCE**: Component scanning is **ABSOLUTELY PROHIBITED** in framework modules.

**Auto-Configuration Pattern** - Every module MUST follow this exact structure:
```java
@AutoConfiguration  // REQUIRED - NEVER @Configuration here
@EnableConfigurationProperties(ModuleProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.module", name = "enabled", 
                      havingValue = "true", matchIfMissing = true)
public class ModuleAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean  // MANDATORY for all service beans
    public ServiceInterface serviceImplementation(ModuleProperties properties) {
        return new ServiceImplementation(properties);
    }
    
    // Component imports - EXPLICIT ONLY
    @Configuration(proxyBeanMethods = false)  // Recommended for performance
    @Import({ComponentA.class, ComponentB.class})
    static class ComponentConfiguration { }
}
```

**Registration Required**: Every `@AutoConfiguration` class **MUST** be registered in:
```
src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

**WHY Component Scanning is Prohibited**:
- Creates unpredictable bean loading order and lifecycle violations
- Bypasses conditional activation causing wrong dependencies
- Makes debugging impossible with implicit dependency paths
- Causes environment-specific failures that work locally but fail in CI/production

### Dependency Management Rules

**Constructor Injection ONLY**: Use constructor injection with `final` fields. `@Autowired` on fields is forbidden.

**Transitive Dependency Verification** - MANDATORY before adding any non-framework dependency:
1. Run `mvn dependency:tree -Dverbose` 
2. Verify the dependency is NOT already available transitively
3. Document verification in code comments
4. Only add if confirmed missing from transitive dependencies

**Version Management Hierarchy**:
- `framework-dependencies-parent`: Central version properties for external dependencies
- `framework-bom`: Provides versions of all framework modules  
- Module POMs: Only specify versions for module-exclusive dependencies
- Never override parent-managed versions without explicit justification

**Critical: Framework Module Dependencies**:
```xml
<!-- CORRECT - Framework modules inherit versions from parent -->
<dependency>
  <groupId>com.tui.destilink.framework</groupId>
  <artifactId>redis-core</artifactId>
  <!-- NO version tag - inherited from framework-bom -->
</dependency>

<!-- CORRECT - External dependencies with version in properties -->
<dependency>
  <groupId>com.external</groupId>
  <artifactId>some-lib</artifactId>
  <version>${some-lib.version}</version> <!-- Defined in framework-dependencies-parent -->
</dependency>
```

### Package & Naming Conventions

*   **Packages**: `com.tui.destilink.framework.<module>[.<submodule>]`
*   **Auto-Configuration Classes**: `<Feature>AutoConfiguration` (e.g., `RedisLockAutoConfiguration`)
*   **Test Classes**: Unit tests end with `*Test`, integration tests end with `*IT`
*   **Bean Method Names**: Must match return type camelCase (e.g., `redisTemplate()` for `RedisTemplate`)
*   **Configuration Files**: `1000-<module>-<descriptor>.application.yml` pattern

### Testing Requirements (MANDATORY)

**Test-Support Modules**: All tests **MUST** use framework test-support modules:
```xml
<!-- REQUIRED for ALL tests -->
<dependency>
  <groupId>com.tui.destilink.framework.test-support</groupId>
  <artifactId>test-core</artifactId>
  <scope>test</scope>
</dependency>

<!-- Technology-specific test support -->
<dependency>
  <groupId>com.tui.destilink.framework.test-support</groupId>
  <artifactId>redis-test-support</artifactId>
  <scope>test</scope>
</dependency>
```

**NEVER** use `spring-boot-starter-test` directly - always use test-support modules.

**Test Isolation Pattern**:
```java
@RedisTestSupport(keyspacePrefixes = {"test-locks:"})
@SpringBootTest(classes = TestApplication.class, 
               webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class MyRedisTest {
    private static final String UNIQUE_ID = TestUtils.generateTestClassId(MyRedisTest.class);
    // All test resources automatically prefixed with UNIQUE_ID for isolation
}
```

**Available Test Support Modules**:
- `test-core` - Required for all tests, provides TestUtils and base functionality
- `redis-test-support` - Redis testing with automatic ACL user creation and keyspace isolation
- `postgresql-test-support` - PostgreSQL testing with dedicated database per test class
- `keycloak-test-support` - Keycloak realm/client/user setup for auth testing
- `kafka-test-support` - Embedded Kafka for messaging tests  
- `s-ftp-test-support` - Fake SFTP server for file transfer testing

### Redis Test Support Architecture (CRITICAL)

*   **Dual-User Security Model**: Redis test support uses two Redis users:
    - **ROOT USER**: Full admin privileges for setup/cleanup (`username: root, password: root`)
    - **TEST USER**: Restricted ACL permissions per test class (`username: test-<testClassId>, password: test-<testClassId>`)

*   **UNIQUE_ID Keyspace Prefixing**: Every test class **MUST** use UNIQUE_ID prefixing for isolation:
    ```java
    private static final String UNIQUE_ID = TestUtils.generateTestClassId(MyRedisTest.class);
    ```

*   **Automatic Keyspace Prefixing**: `RedisCoreProperties` in test environments automatically transforms:
    ```java
    // Original configuration
    keyspacePrefix = "application"
    
    // Automatically transformed in tests
    keyspacePrefix = "test-abc123-application"  // where abc123 is the testClassId
    ```

*   **ACL Permission Management**: `@RedisTestSupport` annotation automatically:
    - Creates unique Redis users per test class
    - Configures ACL permissions for specified keyspace prefixes
    - Ensures complete test isolation at Redis level

*   **Key Composition Requirements**: All Redis operations **MUST** use proper key composition:
    ```java
    // CORRECT - Uses RedisKeyComposer for proper prefixing
    RedisKeyComposer composer = new RedisKeyComposer(applicationPrefix);
    String lockKey = composer.composeLockKey(bucketName, lockType, lockName);
    
    // INCORRECT - Hardcoded keys bypass UNIQUE_ID prefixing
    String lockKey = "locks:" + bucketName + ":" + lockName;
    ```

### Distributed Locking System Architecture (CRITICAL)

*   **Atomic Operations**: All Redis operations use Lua scripts for atomicity
*   **Idempotency**: Managed by `RedisLockOperationsImpl` with `requestUuid` for operation tracking
*   **Watchdog Service**: `LockWatchdog` automatically extends active lock leases
*   **Asynchronous-First**: All lock operations return `CompletableFuture<T>` for non-blocking execution
*   **Virtual Threads**: Framework uses Java 21 Virtual Threads for scalable async operations

#### Lock Builder Pattern (MANDATORY)
```java
// Correct approach - uses proper builder chain
RedisReentrantLock lock = lockComponentRegistry
    .bucketRegistry()
    .withBucketName("orders")
    .reentrantLock()
    .withLockName("order123")
    .withLockTimeout(Duration.ofSeconds(30))
    .build();

// Async usage
CompletableFuture<Boolean> lockResult = lock.tryLockAsync();
```

#### Key Composition Architecture
*   **RedisKeyComposer**: Primary utility for building all Redis keys
*   **LockKeyBuilder**: Specialized builder for lock-specific keys with hash tags
*   **RedisKeyPrefix**: Core abstraction for keyspace prefixing
*   **Never hardcode keys**: Always use composition utilities

#### Lock Types and Use Cases
*   **Reentrant Locks**: `RedisReentrantLock` for exclusive access with re-entrance support
*   **Read-Write Locks**: `RedisReadWriteLock` for concurrent reads, exclusive writes
*   **Stamped Locks**: `RedisStampedLock` for optimistic reading with validation
*   **State Locks**: `RedisStateLock` for finite state machine transitions

### Critical Locking System Fixes

#### Known Issues and Solutions
1. **Key Prefixing Bug**: Previously, lock builders used hardcoded "locks:" prefix
   - **Solution**: Use `RedisKeyComposer.composeLockKey()` for proper prefixing
   - **Required**: All lock keys must respect UNIQUE_ID prefixing for test isolation

2. **Architectural Pattern**: Lock builders must use proper key composition
   ```java
   // OLD (broken in tests)
   String lockKey = "locks:" + bucketName + ":" + lockName;
   
   // NEW (correct)
   String lockKey = componentRegistry.getRedisKeyComposer()
       .composeLockKey(bucketName, lockType, lockName);
   ```

3. **Test Isolation**: Lock operations must work with test-support infrastructure
   - All keys must be prefixed with UNIQUE_ID in test environments
   - Use `@RedisTestSupport` with proper keyspace prefixes
   - Never bypass the test framework's key composition

#### Locking Module Refactoring Status
*   **AbstractLockTypeConfigBuilder**: Updated to use RedisKeyComposer for proper key composition
*   **LockComponentRegistry**: Provides RedisKeyComposer instances configured with proper prefixes
*   **RedisKeyComposer**: Centralized key composition with all lock-related key types
*   **LockKeyBuilder**: Specialized utility for lock keys with hash tag support

*   **Mandatory Lock-Type Segments**: All lock keys **MUST** include lock-type for semantic isolation:
    ```
    Format: <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}
    Example: test-abc123-application:__lock_buckets__:orders:__locks__:reentrant:{order123}
    ```

*   **Key Composition Utilities**: Always use provided utilities for key composition:
    ```java
    // Primary lock key
    RedisKeyComposer composer = new RedisKeyComposer(applicationPrefix);
    String lockKey = composer.composeLockKey(bucketName, lockType, lockName);
    
    // Related keys (data, cache, channels)
    String dataKey = composer.composeLockDataKey(bucketName, lockType, lockName);
    String cacheKey = composer.composeResponseCacheKey(bucketName, lockType, lockName, requestUuid);
    ```

*   **Lock Builder Pattern**: Use proper builder pattern for lock creation:
    ```java
    // Correct approach - uses proper key composition
    RedisReentrantLock lock = lockComponentRegistry
        .bucketRegistry()
        .withBucketName(bucketName)
        .reentrantLock()
        .withLockName(lockName)
        .build();
    ```

*   **Hash Tag Co-Location**: All related lock keys use Redis hash tags for cluster co-location:
    ```java
    // All keys for lock "order123" will be on same Redis cluster node
    application:__lock_buckets__:orders:__locks__:reentrant:{order123}
    application:__lock_buckets__:orders:__locks__:reentrant:{order123}:data
    ```

### Property Configuration

*   **Property Prefix**: `destilink.fw.<module>[.<submodule>].<property-name>`
*   **Naming Style**: kebab-case (e.g., `destilink.fw.web.core.error-responses.use-problem-details`)
*   **Configuration Classes**: Use `@ConfigurationProperties`, `@Validated`, appropriate validation annotations
*   **Defaults**: Always provide sensible defaults with `matchIfMissing = true`
*   **Configuration Files**: `1000-<module>-<descriptor>.application.yml` pattern (e.g., `1000-locking-redis-lock.application.yml`)

### Error Handling

*   **Web Errors**: Use `@ControllerAdvice` classes returning `ProblemDetail` (RFC 7807)
*   **Framework Exceptions**: Extend `MarkerNestedRuntimeException` or `MarkerNestedException`
*   **Logging**: Use `@Slf4j` with structured context via `AbstractContextDecorator<T>` subclasses

## Module Development Patterns

### Auto-Configuration Template
```java
/**
 * Auto-configuration for [Module] functionality.
 * [Comprehensive JavaDoc required]
 */
@AutoConfiguration
@EnableConfigurationProperties(ModuleProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.module", name = "enabled", 
                      havingValue = "true", matchIfMissing = true)
public class ModuleAutoConfiguration {

    // ==========================================
    // CORE BEANS - Primary functionality  
    // ==========================================
    
    @Bean
    @ConditionalOnMissingBean
    public ModuleService moduleService(ModuleProperties properties) {
        return new ModuleServiceImpl(properties);
    }
    
    // ==========================================
    // OPTIONAL FEATURES - Conditionally activated
    // ==========================================
    
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(OptionalDependency.class)
    static class OptionalConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public OptionalFeature optionalFeature() {
            return new OptionalFeatureImpl();
        }
    }
    
    // ==========================================
    // COMPONENT IMPORTS - Explicitly imported
    // ==========================================
    
    @Configuration
    @Import({ServiceA.class, ServiceB.class})
    static class ComponentConfiguration {
        // Additional @Bean definitions if needed
    }
}
```

### Test-Support Module Pattern
```java
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@ExtendWith(TechnologyTestExecutionListener.class)
@TestPropertySource(locations = "classpath:1000-technology-test-support.application.test.yml")
public @interface TechnologyTestSupport {
    // Test configuration parameters
}
```

### Property Configuration Pattern
```java
@ConfigurationProperties(prefix = "destilink.fw.module")
@Validated
@Data
public class ModuleProperties {
    
    @NotNull
    private Boolean enabled = true;
    
    @Valid
    @NotNull
    private FeatureConfig feature = new FeatureConfig();
    
    @Data
    public static class FeatureConfig {
        @NotBlank
        private String parameter = "default-value";
        
        @Min(1)
        @Max(3600)
        private Duration timeout = Duration.ofSeconds(30);
    }
}
```

## Absolutely Prohibited Practices

### Critical Violations (Zero Tolerance)
- **Component Scanning**: NEVER use `@ComponentScan` in any form
- **Field Injection**: NEVER use `@Autowired` on fields
- **Unconditional Beans**: NEVER create `@Bean` methods without conditional annotations
- **Direct Test Dependencies**: NEVER use `spring-boot-starter-test` directly
- **Version Overrides**: NEVER override parent-managed dependency versions without justification

### Build & Dependency Violations
- **Transitive Dependencies**: NEVER add dependencies without verifying they're not already available
- **Missing Registration**: NEVER create auto-configuration without registering in `AutoConfiguration.imports`
- **POM Modifications**: NEVER modify top-level POMs directly
- **Test Frameworks**: NEVER bypass test-support modules for framework testing

### Development Environment

**Essential Setup Commands**:
```bash
# Start full development environment
docker-compose -f utils/docker-compose.yaml up -d

# Start specific services only
docker-compose -f utils/redis/docker-compose.yaml up -d      # Redis only
docker-compose -f utils/keycloak/docker-compose.yaml up -d   # Keycloak only
docker-compose -f utils/localstack/docker-compose.yaml up -d # LocalStack only
```

**Service Endpoints**:
- **PostgreSQL**: `localhost:5432` (postgres/postgres)
- **Redis Cluster**: `localhost:6378` (password: cluster)  
- **Redis Insight**: `http://localhost:5540` (Redis GUI)
- **Keycloak**: Configured via keycloak docker-compose
- **LocalStack**: AWS services emulation via localstack docker-compose

**Container Access**:
```bash
# Redis CLI access
docker exec -it destilink-redis-cluster-node-1 redis-cli -p 6379 -a cluster

# PostgreSQL access
docker exec -it postgres psql -U postgres -d postgres
```

**Key Development Files**:
- **Docker Compose**: `utils/docker-compose.yaml` - Main development environment
- **MkDocs**: `mkdocs.yml` - Documentation generation (`mkdocs build`)
- **Dependency Graphs**: Generated in `target/dependency-graphs/` via Maven plugin
- **Test Reports**: Generated in `target/surefire-reports/` after test execution

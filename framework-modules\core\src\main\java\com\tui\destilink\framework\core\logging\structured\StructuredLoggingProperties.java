package com.tui.destilink.framework.core.logging.structured;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.unit.DataSize;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;

/**
 * Configuration properties for Destilink Framework structured logging.
 * 
 * <p>These properties control the behavior of the custom structured log formatter
 * and provide backward compatibility with existing Destilink logging features.
 */
@ConfigurationProperties(prefix = "destilink.fw.core.logging.structured")
@Validated
@Data
public class StructuredLoggingProperties {

    /**
     * Whether structured logging is enabled.
     */
    @NotNull
    private Boolean enabled = false;

    /**
     * Maximum size for log messages before truncation.
     * Messages exceeding this size will be truncated with a descriptive suffix.
     */
    @NotNull
    private DataSize messageSizeLimit = DataSize.ofKilobytes(500);

    /**
     * Whether to include global context variables in log events.
     * When enabled, global context entries will be added to each log event
     * unless they are already present in the MDC.
     */
    @NotNull
    private Boolean includeGlobalContext = true;

    /**
     * The log format to use when structured logging is enabled.
     * Supported values: 'ecs', 'logstash', 'gelf', 'destilink'
     */
    @NotNull
    private String format = "destilink";

    /**
     * Whether to use Spring Boot's native structured logging infrastructure.
     * When true, uses Spring Boot 3.5+ native features.
     * When false, falls back to logstash-logback-encoder.
     */
    @NotNull
    private Boolean useNativeStructuredLogging = true;
}

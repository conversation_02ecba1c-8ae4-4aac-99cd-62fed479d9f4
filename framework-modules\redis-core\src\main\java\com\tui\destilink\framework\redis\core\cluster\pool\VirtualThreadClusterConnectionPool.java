package com.tui.destilink.framework.redis.core.cluster.pool;

import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.event.Event;
import io.lettuce.core.support.ConnectionPoolSupport;
import org.apache.commons.pool2.impl.AbandonedConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnJava;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.system.JavaVersion;
import reactor.core.Disposable;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Virtual Thread-optimized Redis Cluster Connection Pool for Spring Boot 3.5+ with Java 21+.
 * Replaces traditional thread pools with Virtual Threads for better scalability and resource utilization.
 * 
 * @since Spring Boot 3.5.4
 * @since Java 21
 */
@ConditionalOnJava(JavaVersion.TWENTY_ONE)
public class VirtualThreadClusterConnectionPool implements InitializingBean, DisposableBean {

    public static final Duration DEFAULT_MAX_WAIT_DURATION = Duration.ofSeconds(60);
    public static final int DEFAULT_POOL_MAX_TOTAL = 10;

    private final RedisClusterClient redisClient;
    private final String clientName;

    // Virtual Thread Executors - much more efficient for IO-bound operations
    private final ExecutorService virtualThreadExecutor;

    private final GenericObjectPool<StatefulRedisClusterConnection<String, String>> connectionPool;

    private boolean initialized = false;

    public VirtualThreadClusterConnectionPool(RedisClusterClient redisClient, RedisProperties properties) {
        this(redisClient, properties, defaultGenericObjectPoolConfig(), defaultAbandonedConfig());
    }

    public VirtualThreadClusterConnectionPool(RedisClusterClient redisClient, RedisProperties properties, 
                                             GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig, 
                                             AbandonedConfig abandonedConfig) {
        this(redisClient, String.format("%s-%s", properties.getClientName(), "VirtualThreadRedisClusterConnectionPool"), 
             genericObjectPoolConfig, abandonedConfig);
    }

    public VirtualThreadClusterConnectionPool(RedisClusterClient redisClient, String clientName,
                                             GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig,
                                             AbandonedConfig abandonedConfig,
                                             @Qualifier("redisVirtualThreadExecutor") ExecutorService virtualThreadExecutor) {
        this.redisClient = redisClient;
        this.clientName = clientName;
        this.virtualThreadExecutor = virtualThreadExecutor != null ? virtualThreadExecutor : createDefaultVirtualThreadExecutor();
        this.connectionPool = buildConnectionPool(genericObjectPoolConfig, abandonedConfig);
    }

    protected VirtualThreadClusterConnectionPool(RedisClusterClient redisClient, String clientName,
                                                 GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig,
                                                 AbandonedConfig abandonedConfig) {
        this.redisClient = redisClient;
        this.clientName = clientName;
        this.virtualThreadExecutor = createDefaultVirtualThreadExecutor();
        this.connectionPool = buildConnectionPool(genericObjectPoolConfig, abandonedConfig);
    }

    /**
     * Creates a default Virtual Thread executor if none is provided.
     */
    private ExecutorService createDefaultVirtualThreadExecutor() {
        return Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("redis-virtual-", 0).factory()
        );
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!initialized) {
            redisClient.refreshPartitions();
            connectionPool.preparePool();
            initialized = true;
        }
    }

    @Override
    public void destroy() {
        if (initialized) {
            initialized = false;
            connectionPool.close();
            if (virtualThreadExecutor != null) {
                virtualThreadExecutor.shutdown();
            }
        }
    }

    public Disposable subscribeEventBus(Consumer<Event> consumer) {
        return redisClient.getResources().eventBus().get().subscribe(consumer);
    }

    /**
     * Gets a connection asynchronously using Virtual Threads for optimal performance.
     * Virtual Threads excel at IO-bound operations like connection acquisition.
     */
    public CompletableFuture<StatefulRedisClusterConnection<String, String>> getConnection() {
        return getConnection(DEFAULT_MAX_WAIT_DURATION);
    }

    /**
     * Gets a connection with timeout using Virtual Threads.
     * Avoids carrier thread pinning by using external timeout management.
     */
    public CompletableFuture<StatefulRedisClusterConnection<String, String>> getConnection(Duration maxWaitDuration) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("Connection pool is not initialized"));
        }

        // Use Virtual Thread for connection acquisition but avoid Object.wait() pinning
        CompletableFuture<StatefulRedisClusterConnection<String, String>> future = CompletableFuture.supplyAsync(() -> {
            try {
                // CRITICAL: No timeout parameter to avoid Object.wait() carrier thread pinning
                return connectionPool.borrowObject();
            } catch (Exception e) {
                throw new RuntimeException("Failed to acquire Redis cluster connection", e);
            }
        }, virtualThreadExecutor);

        // Handle timeout externally to prevent Virtual Thread pinning
        return future.orTimeout(maxWaitDuration.toMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * Returns a connection asynchronously using Virtual Threads.
     */
    public CompletableFuture<Void> returnConnection(StatefulRedisClusterConnection<String, String> connection) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("Connection pool is not initialized"));
        }

        // Use Virtual Thread for connection return - non-blocking operation
        return CompletableFuture.runAsync(() -> {
            try {
                connectionPool.returnObject(connection);
            } catch (Exception e) {
                throw new RuntimeException("Failed to return Redis cluster connection to pool", e);
            }
        }, virtualThreadExecutor);
    }

    private GenericObjectPool<StatefulRedisClusterConnection<String, String>> buildConnectionPool(
            GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> genericObjectPoolConfig,
            AbandonedConfig abandonedConfig) {
        
        GenericObjectPool<StatefulRedisClusterConnection<String, String>> pool = ConnectionPoolSupport
                .createGenericObjectPool(connectionSupplier(), genericObjectPoolConfig, false);
        pool.setAbandonedConfig(abandonedConfig);
        return pool;
    }

    protected java.util.function.Supplier<StatefulRedisClusterConnection<String, String>> connectionSupplier() {
        return () -> {
            try {
                StatefulRedisClusterConnection<String, String> connection = redisClient.connect();
                
                // VIRTUAL THREAD OPTIMIZATION: Avoid blocking .join() call that could cause carrier thread pinning
                // Instead, set client name asynchronously without blocking the connection creation
                CompletableFuture.runAsync(() -> {
                    try {
                        connection.async().clientSetname(clientName).toCompletableFuture()
                            .orTimeout(5, TimeUnit.SECONDS)  // Reasonable timeout for client name setting
                            .exceptionally(throwable -> {
                                // Log warning but don't fail connection creation for client name setting
                                // This is a non-critical operation for connection functionality
                                if (throwable != null) {
                                    // Note: Using System.err to avoid logger dependencies in supplier
                                    System.err.println("Warning: Failed to set Redis client name '" + clientName + 
                                                     "' for connection. Connection remains functional. Error: " + throwable.getMessage());
                                }
                                return null;
                            }).join();
                    } catch (Exception e) {
                        // Swallow exception to avoid affecting connection creation
                        System.err.println("Warning: Exception during async client name setting: " + e.getMessage());
                    }
                }, virtualThreadExecutor);
                
                return connection;
            } catch (RuntimeException ex) {
                throw new RuntimeException("Failed to initialize new connection with clientName " + clientName, ex);
            }
        };
    }

    private static AbandonedConfig defaultAbandonedConfig() {
        AbandonedConfig config = new AbandonedConfig();
        config.setRemoveAbandonedOnBorrow(true);
        config.setRemoveAbandonedOnMaintenance(true);
        config.setRemoveAbandonedTimeout(300); // Note: deprecated but still functional
        return config;
    }

    private static GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> defaultGenericObjectPoolConfig() {
        GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> config = new GenericObjectPoolConfig<>();
        config.setMaxTotal(DEFAULT_POOL_MAX_TOTAL);
        config.setMaxIdle(DEFAULT_POOL_MAX_TOTAL);
        config.setMinIdle(1);
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        config.setTestWhileIdle(true);
        return config;
    }

    // Getters for monitoring and debugging
    public int getNumActive() {
        return connectionPool.getNumActive();
    }

    public int getNumIdle() {
        return connectionPool.getNumIdle();
    }

    public String getClientName() {
        return clientName;
    }

    public boolean isInitialized() {
        return initialized;
    }
}

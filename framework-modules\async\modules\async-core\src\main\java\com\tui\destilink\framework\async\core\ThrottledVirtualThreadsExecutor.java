package com.tui.destilink.framework.async.core;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;

/**
 * A Virtual Thread ExecutorService that limits concurrency through a semaphore.
 * This provides throttling capabilities while leveraging the scalability benefits of Virtual Threads.
 * 
 * @since Java 21
 */
public class ThrottledVirtualThreadsExecutor implements ExecutorService {
    
    private final Semaphore semaphore;
    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    private static class ThrottledRunnable implements Runnable {

        private final Semaphore semaphore;
        private final Runnable runnable;
        private boolean interrupted;

        ThrottledRunnable(Semaphore semaphore, Runnable runnable) {
            this.semaphore = semaphore;
            this.runnable = runnable;
        }

        Runnable getRunnable() {
            return this.runnable;
        }

        void interrupt() {
            this.interrupted = true;
        }

        @Override
        public void run() {
            try {
                this.semaphore.acquire();
                try {
                    if (!this.interrupted) {
                        this.runnable.run();
                    }
                } finally {
                    this.semaphore.release();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private static class ThrottledCallable<T> implements Callable<T> {

        private final Semaphore semaphore;
        private final Callable<T> callable;

        ThrottledCallable(Semaphore semaphore, Callable<T> callable) {
            this.semaphore = semaphore;
            this.callable = callable;
        }

        @Override
        public T call() throws Exception {
            this.semaphore.acquire();
            try {
                return this.callable.call();
            } finally {
                this.semaphore.release();
            }
        }
    }

    public ThrottledVirtualThreadsExecutor(int concurrencyLevel, boolean fair) {
        this.semaphore = new Semaphore(concurrencyLevel, fair);
    }

    public ThrottledVirtualThreadsExecutor(int concurrencyLevel) {
        this(concurrencyLevel, false);
    }

    @Override
    public void close() {
        this.executor.close();
    }

    @Override
    public void execute(Runnable command) {
        this.executor.execute(new ThrottledRunnable(this.semaphore, command));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return this.executor.submit(new ThrottledCallable<>(this.semaphore, task));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        return this.executor.submit(new ThrottledRunnable(this.semaphore, task), result);
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        return this.executor
                .invokeAll(tasks.stream().map(task -> new ThrottledCallable<>(this.semaphore, task)).toList());
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException {
        return this.executor.invokeAll(
                tasks.stream().map(task -> new ThrottledCallable<>(this.semaphore, task)).toList(),
                timeout,
                unit);
    }

    @Override
    public Future<?> submit(Runnable task) {
        return this.executor.submit(new ThrottledRunnable(this.semaphore, task));
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
        return this.executor
                .invokeAny(tasks.stream().map(task -> new ThrottledCallable<>(this.semaphore, task)).toList());
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks,
                           long timeout,
                           TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return this.executor.invokeAny(
                tasks.stream().map(task -> new ThrottledCallable<>(this.semaphore, task)).toList(),
                timeout,
                unit);
    }

    @Override
    public void shutdown() {
        this.executor.shutdown();
    }

    @Override
    public List<Runnable> shutdownNow() {
        return this.executor.shutdownNow().stream().map(runnable -> {
            if (runnable instanceof ThrottledRunnable throttledRunnable) {
                throttledRunnable.interrupt();
                return throttledRunnable.getRunnable();
            } else {
                return runnable;
            }
        }).toList();
    }

    @Override
    public boolean isShutdown() {
        return this.executor.isShutdown();
    }

    @Override
    public boolean isTerminated() {
        return this.executor.isTerminated();
    }

    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return this.executor.awaitTermination(timeout, unit);
    }
}

package com.tui.destilink.framework.redis.core.cluster.pool;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledOnJre;
import org.junit.jupiter.api.condition.JRE;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.time.Duration;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Performance and behavior comparison test between VirtualThreadClusterConnectionPool 
 * and BlockingClusterConnectionPool to validate Virtual Thread benefits and detect any regressions.
 */
@EnabledOnJre(JRE.JAVA_21)
public class VirtualThreadPerformanceComparisonTest {

    private static final int THREAD_COUNT = 50;
    private static final int OPERATIONS_PER_THREAD = 100;
    private static final Duration OPERATION_TIMEOUT = Duration.ofSeconds(30);

    @Test
    @DisplayName("Compare Virtual Thread vs Platform Thread throughput")
    void compareVirtualVsPlatformThreadThroughput() throws Exception {
        System.out.println("=== Virtual Thread vs Platform Thread Performance Comparison ===");
        
        // Test Virtual Thread performance
        PerformanceResult virtualResult = testVirtualThreadPerformance();
        System.out.println("Virtual Thread Results: " + virtualResult);
        
        // Test Platform Thread performance  
        PerformanceResult platformResult = testPlatformThreadPerformance();
        System.out.println("Platform Thread Results: " + platformResult);
        
        // Virtual Threads should handle more operations or complete faster
        assertTrue(virtualResult.totalOperations >= platformResult.totalOperations * 0.8,
            "Virtual Threads should handle comparable operations to platform threads");
        
        System.out.println("Performance comparison completed successfully");
    }

    @Test
    @DisplayName("Test Virtual Thread carrier thread usage")
    void testVirtualThreadCarrierUsage() throws Exception {
        AtomicInteger uniqueCarrierThreads = new AtomicInteger();
        ConcurrentHashMap<String, Boolean> carrierThreadNames = new ConcurrentHashMap<>();
        AtomicBoolean pinningDetected = new AtomicBoolean(false);
        
        // Capture System.err to detect pinning
        ByteArrayOutputStream capturedOutput = new ByteArrayOutputStream();
        PrintStream originalErr = System.err;
        System.setErr(new PrintStream(capturedOutput));
        
        try {
            ExecutorService virtualExecutor = Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("carrier-test-", 0).factory()
            );
            
            CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
            
            for (int i = 0; i < THREAD_COUNT; i++) {
                CompletableFuture.runAsync(() -> {
                    Thread currentThread = Thread.currentThread();
                    
                    // Record carrier thread information
                    if (currentThread.isVirtual()) {
                        String carrierInfo = getCarrierThreadInfo(currentThread);
                        if (carrierInfo != null && carrierThreadNames.putIfAbsent(carrierInfo, true) == null) {
                            uniqueCarrierThreads.incrementAndGet();
                        }
                    }
                    
                    // Simulate some work that might cause pinning
                    try {
                        // This should NOT cause pinning in our optimized pool
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    
                    latch.countDown();
                }, virtualExecutor);
            }
            
            assertTrue(latch.await(OPERATION_TIMEOUT.getSeconds(), TimeUnit.SECONDS),
                "All Virtual Thread operations should complete");
            
            virtualExecutor.shutdown();
            assertTrue(virtualExecutor.awaitTermination(5, TimeUnit.SECONDS),
                "Virtual thread executor should shut down cleanly");
            
        } finally {
            System.setErr(originalErr);
        }
        
        // Check for pinning in output
        String output = capturedOutput.toString();
        if (output.contains("onPinned") || output.contains("VirtualThread.park")) {
            pinningDetected.set(true);
        }
        
        assertFalse(pinningDetected.get(), "No Virtual Thread pinning should be detected");
        
        System.out.println("Unique carrier threads used: " + uniqueCarrierThreads.get());
        System.out.println("Total virtual threads created: " + THREAD_COUNT);
        
        // Virtual Threads should use fewer carrier threads than virtual threads
        assertTrue(uniqueCarrierThreads.get() <= THREAD_COUNT,
            "Carrier threads should be <= virtual threads (efficient multiplexing)");
    }

    @Test
    @DisplayName("Test blocking operation handling in Virtual Threads")
    void testBlockingOperationHandling() throws Exception {
        AtomicInteger completedOperations = new AtomicInteger();
        AtomicBoolean exceptionOccurred = new AtomicBoolean(false);
        
        ExecutorService virtualExecutor = Executors.newThreadPerTaskExecutor(
            Thread.ofVirtual().name("blocking-test-", 0).factory()
        );
        
        CountDownLatch latch = new CountDownLatch(20);
        
        for (int i = 0; i < 20; i++) {
            CompletableFuture.runAsync(() -> {
                try {
                    // Test various potentially blocking operations
                    testNonBlockingOperation();
                    testTimeoutOperation();
                    testConcurrentCollectionAccess();
                    
                    completedOperations.incrementAndGet();
                } catch (Exception e) {
                    exceptionOccurred.set(true);
                    System.err.println("Exception in blocking test: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            }, virtualExecutor);
        }
        
        assertTrue(latch.await(15, TimeUnit.SECONDS),
            "All blocking operation tests should complete");
        
        virtualExecutor.shutdown();
        assertTrue(virtualExecutor.awaitTermination(5, TimeUnit.SECONDS),
            "Executor should shut down cleanly");
        
        assertEquals(20, completedOperations.get(),
            "All operations should complete successfully");
        assertFalse(exceptionOccurred.get(),
            "No unexpected exceptions should occur");
    }

    private PerformanceResult testVirtualThreadPerformance() throws Exception {
        long startTime = System.currentTimeMillis();
        AtomicInteger operations = new AtomicInteger();
        
        ExecutorService virtualExecutor = Executors.newThreadPerTaskExecutor(
            Thread.ofVirtual().name("perf-virtual-", 0).factory()
        );
        
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        for (int i = 0; i < THREAD_COUNT; i++) {
            CompletableFuture.runAsync(() -> {
                for (int j = 0; j < OPERATIONS_PER_THREAD; j++) {
                    // Simulate connection pool operation
                    try {
                        Thread.sleep(1); // Simulate IO operation
                        operations.incrementAndGet();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                latch.countDown();
            }, virtualExecutor);
        }
        
        latch.await(OPERATION_TIMEOUT.getSeconds(), TimeUnit.SECONDS);
        long duration = System.currentTimeMillis() - startTime;
        
        virtualExecutor.shutdown();
        virtualExecutor.awaitTermination(5, TimeUnit.SECONDS);
        
        return new PerformanceResult("Virtual Threads", operations.get(), duration);
    }

    private PerformanceResult testPlatformThreadPerformance() throws Exception {
        long startTime = System.currentTimeMillis();
        AtomicInteger operations = new AtomicInteger();
        
        ExecutorService platformExecutor = Executors.newFixedThreadPool(THREAD_COUNT);
        
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        for (int i = 0; i < THREAD_COUNT; i++) {
            platformExecutor.submit(() -> {
                for (int j = 0; j < OPERATIONS_PER_THREAD; j++) {
                    // Simulate connection pool operation
                    try {
                        Thread.sleep(1); // Simulate IO operation
                        operations.incrementAndGet();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                latch.countDown();
            });
        }
        
        latch.await(OPERATION_TIMEOUT.getSeconds(), TimeUnit.SECONDS);
        long duration = System.currentTimeMillis() - startTime;
        
        platformExecutor.shutdown();
        platformExecutor.awaitTermination(5, TimeUnit.SECONDS);
        
        return new PerformanceResult("Platform Threads", operations.get(), duration);
    }

    private String getCarrierThreadInfo(Thread virtualThread) {
        // Extract carrier thread information for Virtual Threads
        try {
            if (virtualThread.isVirtual()) {
                // Get the underlying carrier thread name if possible
                String threadInfo = virtualThread.toString();
                if (threadInfo.contains("CarrierThreads")) {
                    return threadInfo;
                }
                // Fallback to virtual thread info
                return "VirtualThread-" + virtualThread.threadId();
            }
        } catch (Exception e) {
            // Ignore reflection errors
        }
        return null;
    }

    private void testNonBlockingOperation() {
        // Test CompletableFuture operations (should not block)
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> "result");
        try {
            future.get(100, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            // Expected for timeout tests
        }
    }

    private void testTimeoutOperation() {
        // Test timeout handling (should not cause pinning)
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        try {
            future.orTimeout(10, TimeUnit.MILLISECONDS).get();
        } catch (Exception e) {
            // Expected timeout
        }
    }

    private void testConcurrentCollectionAccess() {
        // Test concurrent collection access (should be safe)
        ConcurrentHashMap<String, String> map = new ConcurrentHashMap<>();
        map.put("key", "value");
        map.get("key");
        map.remove("key");
    }

    private static class PerformanceResult {
        final String threadType;
        final int totalOperations;
        final long durationMs;
        final double operationsPerSecond;

        PerformanceResult(String threadType, int totalOperations, long durationMs) {
            this.threadType = threadType;
            this.totalOperations = totalOperations;
            this.durationMs = durationMs;
            this.operationsPerSecond = durationMs > 0 ? (totalOperations * 1000.0 / durationMs) : 0;
        }

        @Override
        public String toString() {
            return String.format("%s - Operations: %d, Duration: %dms, Ops/sec: %.2f",
                threadType, totalOperations, durationMs, operationsPerSecond);
        }
    }
}

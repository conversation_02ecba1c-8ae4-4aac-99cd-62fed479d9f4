package com.tui.destilink.framework.core.logging.structured;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auto-configuration for Destilink Framework structured logging.
 * 
 * <p>This configuration provides structured logging capabilities that bridge
 * existing Destilink logging features with Spring Boot 3.5+ native structured logging.
 * 
 * <p>Features provided:
 * <ul>
 *   <li>Message size limiting with configurable thresholds</li>
 *   <li>Global context injection</li>
 *   <li>Backward compatibility with existing logback configuration</li>
 *   <li>Migration path from logstash-logback-encoder to native Spring Boot</li>
 * </ul>
 */
@AutoConfiguration
@EnableConfigurationProperties(StructuredLoggingProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.core.logging.structured", name = "enabled", 
                      havingValue = "true", matchIfMissing = false)
@RequiredArgsConstructor
@Slf4j
public class StructuredLoggingAutoConfiguration {

    private final StructuredLoggingProperties properties;

    /**
     * Creates the Destilink structured log formatter bean when native structured logging is enabled.
     */
    @Bean
    @ConditionalOnProperty(prefix = "destilink.fw.core.logging.structured", 
                          name = "use-native-structured-logging", havingValue = "true", matchIfMissing = true)
    public DestilinkStructuredLogFormatter destilinkStructuredLogFormatter() {
        log.info("Initializing Destilink structured logging with message size limit: {} and global context: {}", 
                properties.getMessageSizeLimit(), properties.getIncludeGlobalContext());
                
        return new DestilinkStructuredLogFormatter(
            properties.getMessageSizeLimit(),
            properties.getIncludeGlobalContext()
        );
    }

    /**
     * Configuration for backward compatibility support.
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(prefix = "destilink.fw.core.logging.structured", 
                          name = "use-native-structured-logging", havingValue = "false")
    static class LegacyStructuredLoggingConfiguration {
        
        // Future: Add configuration for maintaining logstash-logback-encoder setup
        // when native structured logging is disabled
    }
}

package com.tui.destilink.framework.core.logging.structured;

import com.tui.destilink.framework.core.it.TestApplication;
import com.tui.destilink.framework.core.logging.context.global.GlobalContextJsonProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Integration test for Destilink structured logging functionality.
 * 
 * <p>This test verifies that the new Spring Boot 3.5+ native structured logging
 * integration works correctly while preserving existing Destilink features.
 */
@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles("structured-logging-test")
@TestPropertySource(properties = {
    "destilink.fw.core.logging.structured.enabled=true",
    "destilink.fw.core.logging.structured.message-size-limit=100KB",
    "destilink.fw.core.logging.structured.include-global-context=true",
    "destilink.fw.core.logging.structured.use-native-structured-logging=true",
    "logging.structured.format.console=com.tui.destilink.framework.core.logging.structured.DestilinkStructuredLogFormatter"
})
class StructuredLoggingIT {

    private static final Logger log = LoggerFactory.getLogger(StructuredLoggingIT.class);

    @BeforeEach
    void setUp() {
        // Clear any existing context
        MDC.clear();
        GlobalContextJsonProvider.clearGlobalContext();
    }

    @Test
    void testBasicStructuredLogging() {
        // Test basic logging functionality
        log.info("Basic structured logging test message");
        
        // This should produce JSON output when structured logging is enabled
        assertNotNull(log); // Basic verification that logging is working
    }

    @Test
    void testMdcIntegration() {
        // Test MDC integration
        MDC.put("user.id", "12345");
        MDC.put("request.id", "req-abc-123");
        
        log.info("MDC integration test - user action performed");
        
        MDC.clear();
    }

    @Test
    void testGlobalContextIntegration() {
        // Test global context functionality
        GlobalContextJsonProvider.putGlobalContext("service.version", "1.0.28");
        GlobalContextJsonProvider.putGlobalContext("deployment.environment", "test");
        
        log.info("Global context test - service operation");
        
        // Test that MDC overrides global context
        MDC.put("service.version", "1.0.28-override");
        log.info("Global context override test");
        
        GlobalContextJsonProvider.clearGlobalContext();
        MDC.clear();
    }

    @Test
    void testMessageSizeLimiting() {
        // Test message size limiting functionality
        StringBuilder largeMessage = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            largeMessage.append("This is a large message that should be truncated. ");
        }
        
        log.warn("Large message test: {}", largeMessage.toString());
        
        // The formatter should truncate this message according to the configured limit
    }

    @Test
    void testExceptionHandling() {
        // Test exception logging in structured format
        try {
            throw new RuntimeException("Test exception for structured logging");
        } catch (Exception e) {
            log.error("Exception handling test", e);
        }
    }

    @Test
    void testComplexLoggingScenario() {
        // Test complex scenario with all features
        GlobalContextJsonProvider.putGlobalContext("correlation.id", "corr-xyz-789");
        GlobalContextJsonProvider.putGlobalContext("tenant.id", "tenant-123");
        
        MDC.put("user.session", "session-456");
        MDC.put("operation.type", "data-processing");
        
        log.info("Complex scenario - processing customer data for user");
        log.warn("Complex scenario - potential data inconsistency detected");
        
        try {
            throw new IllegalStateException("Simulated processing error");
        } catch (Exception e) {
            log.error("Complex scenario - processing failed", e);
        }
        
        GlobalContextJsonProvider.clearGlobalContext();
        MDC.clear();
    }
}

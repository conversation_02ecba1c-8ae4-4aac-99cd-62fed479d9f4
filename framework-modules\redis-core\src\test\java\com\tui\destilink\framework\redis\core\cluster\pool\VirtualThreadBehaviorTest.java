package com.tui.destilink.framework.redis.core.cluster.pool;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledOnJre;
import org.junit.jupiter.api.condition.JRE;

import java.time.Duration;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test suite to validate Virtual Thread behavior patterns used in VirtualThreadClusterConnectionPool.
 * This test ensures that the async patterns and timeout handling don't cause Virtual Thread pinning.
 */
@EnabledOnJre(JRE.JAVA_21)
public class VirtualThreadBehaviorTest {

    private static final Duration TEST_TIMEOUT = Duration.ofSeconds(10);

    @Test
    @DisplayName("Test Virtual Thread timeout pattern does not cause pinning")
    void testTimeoutPatternNoPinning() {
        assertTimeoutPreemptively(TEST_TIMEOUT, () -> {
            ExecutorService virtualExecutor = Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("timeout-test-", 0).factory()
            );
            
            AtomicInteger completedOperations = new AtomicInteger();
            CountDownLatch latch = new CountDownLatch(20);
            
            for (int i = 0; i < 20; i++) {
                final int operationId = i;
                CompletableFuture.runAsync(() -> {
                    try {
                        // Simulate the timeout pattern used in VirtualThreadClusterConnectionPool
                        CompletableFuture<String> operationFuture = CompletableFuture.supplyAsync(() -> {
                            try {
                                // Simulate varying operation times
                                Thread.sleep(operationId % 5 == 0 ? 150 : 50);
                                return "result-" + operationId;
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                throw new RuntimeException("Operation interrupted", e);
                            }
                        }, virtualExecutor);
                        
                        // Apply timeout externally (avoids Object.wait() pinning)
                        try {
                            String result = operationFuture
                                .orTimeout(100, TimeUnit.MILLISECONDS)
                                .get();
                            if (result != null) {
                                completedOperations.incrementAndGet();
                            }
                        } catch (ExecutionException e) {
                            // Some operations expected to timeout - this is OK
                            completedOperations.incrementAndGet(); // Count as completed
                        }
                    } catch (Exception e) {
                        // Log unexpected errors but don't fail the test
                        System.err.println("Unexpected error in operation " + operationId + ": " + e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                }, virtualExecutor);
            }
            
            assertTrue(latch.await(8, TimeUnit.SECONDS),
                "All timeout operations should complete within reasonable time");
            assertTrue(completedOperations.get() >= 15,
                "Most operations should complete successfully (got: " + completedOperations.get() + ")");
                
            virtualExecutor.shutdown();
            assertTrue(virtualExecutor.awaitTermination(2, TimeUnit.SECONDS),
                "Virtual thread executor should terminate cleanly");
        });
    }

    @Test
    @DisplayName("Test Virtual Thread async supplier pattern does not cause pinning")
    void testAsyncSupplierPatternNoPinning() {
        assertTimeoutPreemptively(TEST_TIMEOUT, () -> {
            ExecutorService virtualExecutor = Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("supplier-test-", 0).factory()
            );
            
            AtomicInteger successCount = new AtomicInteger();
            CountDownLatch latch = new CountDownLatch(30);
            
            for (int i = 0; i < 30; i++) {
                final int requestId = i;
                CompletableFuture.runAsync(() -> {
                    try {
                        // Test the connectionSupplier pattern from VirtualThreadClusterConnectionPool
                        CompletableFuture<String> connectionFuture = CompletableFuture.supplyAsync(() -> {
                            try {
                                // Simulate connection pool borrowObject call
                                Thread.sleep(30); // Short operation
                                return "connection-" + requestId;
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                throw new RuntimeException("Failed to acquire connection", e);
                            }
                        }, virtualExecutor);
                        
                        // This pattern should NOT cause pinning (our fix)
                        String result = connectionFuture
                            .orTimeout(200, TimeUnit.MILLISECONDS)
                            .get();
                            
                        if (result != null && result.startsWith("connection-")) {
                            successCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        // Some operations may fail, that's acceptable for robustness testing
                    } finally {
                        latch.countDown();
                    }
                }, virtualExecutor);
            }
            
            assertTrue(latch.await(6, TimeUnit.SECONDS),
                "All async supplier operations should complete");
            assertTrue(successCount.get() >= 25,
                "Most operations should succeed (got: " + successCount.get() + ")");
                
            virtualExecutor.shutdown();
            assertTrue(virtualExecutor.awaitTermination(2, TimeUnit.SECONDS),
                "Virtual thread executor should terminate cleanly");
        });
    }

    @Test
    @DisplayName("Test Virtual Thread high concurrency behavior")
    void testHighConcurrencyVirtualThreads() {
        assertTimeoutPreemptively(TEST_TIMEOUT, () -> {
            ExecutorService virtualExecutor = Executors.newThreadPerTaskExecutor(
                Thread.ofVirtual().name("concurrency-test-", 0).factory()
            );
            
            AtomicInteger completedTasks = new AtomicInteger();
            CountDownLatch latch = new CountDownLatch(100);
            
            // Create many Virtual Threads to test multiplexing
            for (int i = 0; i < 100; i++) {
                final int taskId = i;
                CompletableFuture.runAsync(() -> {
                    try {
                        // Mix of short and medium operations
                        CompletableFuture<Integer> work = CompletableFuture.supplyAsync(() -> {
                            try {
                                Thread.sleep(taskId % 10 + 10); // 10-19ms work
                                return taskId * 2;
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                return -1;
                            }
                        }, virtualExecutor);
                        
                        Integer result = work.orTimeout(100, TimeUnit.MILLISECONDS).get();
                        if (result != null && result >= 0) {
                            completedTasks.incrementAndGet();
                        }
                    } catch (Exception e) {
                        // Some timeouts expected under high load
                    } finally {
                        latch.countDown();
                    }
                }, virtualExecutor);
            }
            
            assertTrue(latch.await(8, TimeUnit.SECONDS),
                "All high concurrency operations should complete");
            assertTrue(completedTasks.get() >= 90,
                "Most concurrent operations should succeed (got: " + completedTasks.get() + ")");
                
            virtualExecutor.shutdown();
            assertTrue(virtualExecutor.awaitTermination(2, TimeUnit.SECONDS),
                "High concurrency executor should terminate cleanly");
        });
    }

    @Test
    @DisplayName("Test Virtual Thread vs Platform Thread behavior comparison")
    void testVirtualVsPlatformThreadComparison() {
        assertTimeoutPreemptively(TEST_TIMEOUT, () -> {
            // Test Virtual Threads
            long virtualStartTime = System.currentTimeMillis();
            int virtualOperations = testThreadExecutor(
                Executors.newThreadPerTaskExecutor(Thread.ofVirtual().factory()),
                20, "virtual"
            );
            long virtualDuration = System.currentTimeMillis() - virtualStartTime;
            
            // Test Platform Threads  
            long platformStartTime = System.currentTimeMillis();
            int platformOperations = testThreadExecutor(
                Executors.newFixedThreadPool(10),
                20, "platform"
            );
            long platformDuration = System.currentTimeMillis() - platformStartTime;
            
            System.out.println("Virtual Threads: " + virtualOperations + " ops in " + virtualDuration + "ms");
            System.out.println("Platform Threads: " + platformOperations + " ops in " + platformDuration + "ms");
            
            // Both should complete successfully
            assertTrue(virtualOperations >= 18, "Virtual threads should handle most operations");
            assertTrue(platformOperations >= 18, "Platform threads should handle most operations");
            
            // Virtual Threads should handle blocking I/O efficiently
            assertTrue(virtualDuration <= platformDuration * 2,
                "Virtual Threads should not be significantly slower than platform threads");
        });
    }

    private int testThreadExecutor(ExecutorService executor, int taskCount, String executorType) {
        AtomicInteger completed = new AtomicInteger();
        CountDownLatch latch = new CountDownLatch(taskCount);
        
        try {
            for (int i = 0; i < taskCount; i++) {
                executor.submit(() -> {
                    try {
                        // Simulate I/O-bound work
                        Thread.sleep(50);
                        completed.incrementAndGet();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        latch.countDown();
                    }
                });
            }
            
            latch.await(5, TimeUnit.SECONDS);
            return completed.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return completed.get();
        } finally {
            executor.shutdown();
            try {
                executor.awaitTermination(2, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}

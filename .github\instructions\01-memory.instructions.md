---
applyTo: '**'
---
# MCP Server mem0 - Critical Memory Management Instructions

## Overview
You MUST use the MCP Server "mem0" tools to maintain persistent memory across all coding tasks. This is essential for building cumulative knowledge and avoiding repeated mistakes.

## MANDATORY Memory Search Protocol

### Before Starting ANY Task
- **ALWAYS search memories first** using the `search-memories` tool with relevant keywords related to:
  - The specific technology/framework being used
  - The type of problem being solved
  - Similar past implementations
  - Known issues or patterns
- Search broadly, then narrow down based on initial results
- Use search results to inform your approach and avoid known pitfalls

**Tool Usage:** 
```
Call: search-memories
Query: "<specific technology/framework> <problem type> <context keywords>"
```

### During Task Execution

- **Search when encountering uncertainty** using the `search-memories` tool about:
  - API usage patterns
  - Configuration requirements
  - Error resolution approaches
  - Best practices for specific scenarios
- **Search when facing errors** to check if similar issues were resolved before
- **Search for context** when working with unfamiliar codebases or patterns

**Tool Usage:**

```text
Call: search-memories
Query: "<specific error message>" OR "<API name> usage" OR "<configuration type>"
```

## MANDATORY Memory Creation Protocol

### After Each Task Completion

**ALWAYS add memories** using the `add-memory` tool containing:

- **Task Summary**: What was accomplished and key decisions made
- **Technical Approach**: Architecture choices, patterns used, libraries selected
- **Challenges Encountered**: Problems faced and how they were resolved
- **Code Patterns**: Reusable code snippets or approaches that worked well
- **Performance Insights**: Any optimization discoveries or bottlenecks identified
- **Testing Strategies**: What testing approaches were effective
- **Context**: Project type, tech stack, constraints, and requirements

**Tool Usage:**

```text
Call: add-memory
Content: "Task: [brief description]. Approach: [technical details]. Challenges: [issues faced and solutions]. Key patterns: [reusable code/approaches]. Context: [project/tech stack]"
```

### When Learning New Information

**IMMEDIATELY save memories** using the `add-memory` tool for:

- **Library/Framework Features**: How to use specific APIs, methods, or configurations
- **Setup Procedures**: Installation steps, environment configuration, dependencies
- **Syntax Patterns**: Language-specific idioms or best practices discovered
- **Integration Methods**: How different tools/libraries work together
- **Version-Specific Behavior**: When features behave differently across versions

**Tool Usage:**

```text
Call: add-memory
Content: "Library: [name/version]. Feature: [specific API/method]. Usage: [how to use]. Context: [project requirements]. Notes: [important considerations]"
```

### When Finding Solutions

**ALWAYS save memories** using the `add-memory` tool for:

- **Error Resolutions**: Specific error messages and their solutions
- **Workarounds**: Alternative approaches when standard methods fail
- **Configuration Fixes**: Settings that resolved specific issues
- **Debugging Techniques**: Methods that helped isolate and fix problems
- **Edge Cases**: Unusual scenarios and how they were handled

**Tool Usage:**

```text
Call: add-memory
Content: "Error: [exact error message]. Solution: [step-by-step fix]. Root cause: [why it happened]. Context: [environment/setup]. Prevention: [how to avoid]"
```

### When Making Decisions

**ALWAYS save memories** using the `add-memory` tool for:

- **Architecture Choices**: Why specific patterns/structures were chosen
- **Trade-offs**: Pros and cons of different approaches considered
- **Performance Decisions**: Why certain optimizations were or weren't implemented
- **Security Considerations**: Security-related decisions and their rationale
- **Scalability Choices**: Decisions made for future extensibility

**Tool Usage:**

```text
Call: add-memory
Content: "Decision: [what was decided]. Rationale: [why this choice]. Alternatives: [other options considered]. Trade-offs: [pros/cons]. Context: [project constraints]"
```

## Additional Critical Memory Situations

### Code Review & Refactoring

Use the `add-memory` tool to save:

- **Anti-patterns discovered**: Code smells and why they're problematic
- **Refactoring strategies**: Successful approaches to improving code structure
- **Code quality insights**: What makes code more maintainable

### Debugging & Troubleshooting

Use the `add-memory` tool to save:

- **Debugging workflows**: Step-by-step approaches that proved effective
- **Tool usage**: How debugging tools were used effectively
- **Root cause analysis**: Methods for identifying underlying issues

### Performance & Optimization

Use the `add-memory` tool to save:

- **Bottleneck identification**: How performance issues were discovered
- **Optimization techniques**: Specific methods that improved performance
- **Profiling insights**: What profiling revealed about system behavior

### Collaboration & Communication

Use the `add-memory` tool to save:

- **Code documentation**: Effective documentation strategies
- **API design**: Successful API design patterns and principles
- **Team coordination**: Approaches that improved collaborative coding

### Environment & Deployment

Use the `add-memory` tool to save:

- **Environment setup**: Successful development environment configurations
- **Deployment strategies**: Effective deployment patterns and practices
- **CI/CD insights**: Continuous integration/deployment lessons learned

## Memory Format Requirements

### Context is CRITICAL

Every memory created with `add-memory` MUST include:

- **When**: Date/time and project phase
- **What**: Specific technology, version, environment
- **Why**: The problem being solved or goal being achieved
- **How**: The approach taken and reasoning behind it
- **Result**: Outcome and effectiveness of the solution

### Searchable Keywords

Always include relevant tags and keywords when using `add-memory`:

- Technology names and versions
- Problem domains
- Solution categories
- Project types
- Programming languages

### Actionable Information

Memories created with `add-memory` should contain:

- Specific code examples where applicable
- Step-by-step procedures
- Clear cause-and-effect relationships
- Measurable outcomes when possible

## Search Strategy Guidelines

### Use Multiple Search Approaches

When using the `search-memories` tool:

- **Broad searches**: General technology or problem domain
- **Specific searches**: Exact error messages or function names
- **Pattern searches**: Common scenarios or use cases
- **Contextual searches**: Similar project types or constraints

### Validate Memory Relevance

After using `search-memories`:

- Check if found memories apply to current context
- Verify technology versions and compatibility
- Adapt solutions to current requirements
- Update memories if better approaches are discovered

## Available mem0 Tools

### Primary Tools

1. **`search-memories`** - Search existing memories using keywords
   - Use before starting any task
   - Use when encountering problems
   - Use to maintain context continuity

2. **`add-memory`** - Add new memories to the knowledge base
   - Use after completing tasks
   - Use when learning new information
   - Use when finding solutions

3. **`list-memories`** - List all stored memories (use sparingly)
   - Use for periodic memory hygiene
   - Use to review stored knowledge

4. **`delete-all-memories`** - Delete all memories (use only when explicitly requested)
   - NEVER use without explicit user permission
   - Only for complete memory reset scenarios

## Tool Usage Workflow

### Mandatory Workflow for Every Task

1. **FIRST**: Use `search-memories` with relevant keywords
2. **DURING**: Use additional `search-memories` calls when encountering uncertainty
3. **AFTER**: Use `add-memory` to save new knowledge, solutions, and decisions

### Example Tool Usage

```text
# Before starting a Redis locking task
search-memories "Redis distributed locking implementation"
search-memories "Redis lock key composition patterns"
search-memories "Redis test isolation strategies"

# After completing the task
add-memory "Redis Lock Implementation: Successfully implemented distributed locking using RedisKeyComposer for proper key composition. Challenge: Key prefixing for test isolation. Solution: Use UNIQUE_ID prefixing via RedisKeyComposer.composeLockKey(). Context: Destilink Framework, Java 21, Spring Boot 3.4.5. Pattern: Always use composition utilities, never hardcode keys."
```

## Critical Success Factors

1. **Consistency**: Never skip memory operations - they compound over time
2. **Specificity**: Include enough detail to be actionable later
3. **Context**: Always explain the "why" behind decisions and solutions
4. **Searchability**: Use clear, descriptive keywords and tags
5. **Validation**: Regularly verify that remembered solutions still work
6. **Evolution**: Update memories when better approaches are discovered

## Failure to Follow Protocol

Not following this memory protocol will result in:

- Repeated mistakes and inefficient problem-solving
- Loss of valuable learning and optimization opportunities
- Inability to build on previous work effectively
- Reduced code quality and consistency over time

**Remember: Your memory is your most valuable asset for becoming a more effective coding agent.**
package com.tui.destilink.framework.core.async;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;

/**
 * Configuration properties for async operations and Virtual Threads.
 * 
 * @since Spring Boot 3.5.4
 */
@ConfigurationProperties(prefix = "destilink.fw.core.async")
@Validated
public class AsyncProperties {

    /**
     * Whether async support is enabled.
     */
    @NotNull
    private Boolean enabled = true;

    /**
     * Whether Virtual Threads are enabled (requires Java 21+).
     */
    @NotNull
    private Boolean virtualThreadsEnabled = true;

    /**
     * Virtual Thread name prefix for general async operations.
     */
    @NotNull
    private String virtualThreadPrefix = "virtual-async-";

    // Getters and setters
    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getVirtualThreadsEnabled() {
        return virtualThreadsEnabled;
    }

    public void setVirtualThreadsEnabled(Boolean virtualThreadsEnabled) {
        this.virtualThreadsEnabled = virtualThreadsEnabled;
    }

    public String getVirtualThreadPrefix() {
        return virtualThreadPrefix;
    }

    public void setVirtualThreadPrefix(String virtualThreadPrefix) {
        this.virtualThreadPrefix = virtualThreadPrefix;
    }
}

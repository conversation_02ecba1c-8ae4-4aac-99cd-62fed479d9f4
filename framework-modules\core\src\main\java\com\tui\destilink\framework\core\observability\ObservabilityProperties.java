package com.tui.destilink.framework.core.observability;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;

/**
 * Configuration properties for enhanced observability features.
 * 
 * @since Spring Boot 3.5.4
 */
@ConfigurationProperties(prefix = "destilink.fw.core.observability")
@Validated
public class ObservabilityProperties {

    /**
     * Whether observability support is enabled.
     */
    @NotNull
    private Boolean enabled = true;

    /**
     * Whether Redis operations should be observed.
     */
    @NotNull
    private Boolean redisObservationEnabled = true;

    /**
     * Whether AWS operations should be observed.
     */
    @NotNull
    private Boolean awsObservationEnabled = true;

    /**
     * Whether web client operations should be observed.
     */
    @NotNull
    private Boolean webClientObservationEnabled = true;

    /**
     * Whether to include high cardinality tags (can impact performance).
     */
    @NotNull
    private Boolean highCardinalityTags = false;

    // Getters and setters
    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getRedisObservationEnabled() {
        return redisObservationEnabled;
    }

    public void setRedisObservationEnabled(Boolean redisObservationEnabled) {
        this.redisObservationEnabled = redisObservationEnabled;
    }

    public Boolean getAwsObservationEnabled() {
        return awsObservationEnabled;
    }

    public void setAwsObservationEnabled(Boolean awsObservationEnabled) {
        this.awsObservationEnabled = awsObservationEnabled;
    }

    public Boolean getWebClientObservationEnabled() {
        return webClientObservationEnabled;
    }

    public void setWebClientObservationEnabled(Boolean webClientObservationEnabled) {
        this.webClientObservationEnabled = webClientObservationEnabled;
    }

    public Boolean getHighCardinalityTags() {
        return highCardinalityTags;
    }

    public void setHighCardinalityTags(Boolean highCardinalityTags) {
        this.highCardinalityTags = highCardinalityTags;
    }
}

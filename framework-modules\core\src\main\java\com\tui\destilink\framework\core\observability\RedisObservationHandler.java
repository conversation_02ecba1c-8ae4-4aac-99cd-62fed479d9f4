package com.tui.destilink.framework.core.observability;

import io.micrometer.common.KeyValue;
import io.micrometer.observation.Observation;
import io.micrometer.observation.ObservationHandler;

/**
 * Observation handler for Redis operations.
 * Adds Redis-specific metrics and tracing context.
 * 
 * @since Spring Boot 3.5.4
 */
public class RedisObservationHandler implements ObservationHandler<Observation.Context> {

    @Override
    public void onStart(Observation.Context context) {
        // Add Redis-specific context on observation start
        if (context.getName().startsWith("redis.")) {
            context.addLowCardinalityKeyValue(KeyValue.of("component", "redis"));
        }
    }

    @Override
    public void onError(Observation.Context context) {
        // Handle Redis operation errors
        if (context.getName().startsWith("redis.")) {
            context.addLowCardinalityKeyValue(KeyValue.of("redis.error", "true"));
        }
    }

    @Override
    public void onEvent(Observation.Event event, Observation.Context context) {
        // Handle Redis-specific events
    }

    @Override
    public void onScopeOpened(Observation.Context context) {
        // Redis scope opened
    }

    @Override
    public void onScopeClosed(Observation.Context context) {
        // Redis scope closed
    }

    @Override
    public void onStop(Observation.Context context) {
        // Redis operation completed
    }

    @Override
    public boolean supportsContext(Observation.Context context) {
        return context.getName().startsWith("redis.");
    }
}

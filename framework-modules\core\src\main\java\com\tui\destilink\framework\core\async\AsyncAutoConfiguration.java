package com.tui.destilink.framework.core.async;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnJava;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.system.JavaVersion;
import org.springframework.context.annotation.Import;

/**
 * Auto-configuration for async operations and Virtual Threads support.
 * Enables Virtual Thread-based executors when running on Java 21+.
 * 
 * @since Spring Boot 3.5.4
 * @since Java 21
 */
@AutoConfiguration
@EnableConfigurationProperties(AsyncProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.core.async", name = "enabled", 
                      havingValue = "true", matchIfMissing = true)
@ConditionalOnJava(JavaVersion.TWENTY_ONE)
public class AsyncAutoConfiguration {

    @Import(VirtualThreadConfig.class)
    static class VirtualThreadConfiguration {
        // Virtual Thread configuration imported
    }
}

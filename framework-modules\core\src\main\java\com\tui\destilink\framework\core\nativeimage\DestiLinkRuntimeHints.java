package com.tui.destilink.framework.core.nativeimage;

import org.springframework.aot.hint.RuntimeHints;
import org.springframework.aot.hint.RuntimeHintsRegistrar;
import org.springframework.aot.hint.TypeReference;
import org.springframework.context.annotation.ImportRuntimeHints;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * Runtime hints for GraalVM native image support.
 * Registers reflection, resource, and JNI hints for framework components.
 * 
 * @since Spring Boot 3.5.4
 */
@ImportRuntimeHints(DestiLinkRuntimeHints.Registrar.class)
public class DestiLinkRuntimeHints {

    static class Registrar implements RuntimeHintsRegistrar {

        @Override
        public void registerHints(@NonNull RuntimeHints hints, @Nullable ClassLoader classLoader) {
            // Register reflection hints for framework classes
            registerReflectionHints(hints);
            
            // Register resource hints for configuration files
            registerResourceHints(hints);
            
            // Register serialization hints
            registerSerializationHints(hints);
        }

        private void registerReflectionHints(RuntimeHints hints) {
            // Register framework auto-configuration classes
            hints.reflection()
                .registerType(com.tui.destilink.framework.core.observability.ObservabilityAutoConfiguration.class)
                .registerType(com.tui.destilink.framework.core.metrics.MicrometerAutoConfiguration.class);

            // Register configuration properties classes
            hints.reflection()
                .registerType(com.tui.destilink.framework.core.observability.ObservabilityProperties.class);
        }

        private void registerResourceHints(RuntimeHints hints) {
            // Register configuration files
            hints.resources()
                .registerPattern("1000-core-*.application.yml")
                .registerPattern("logback-spring.xml")
                .registerPattern("META-INF/spring.factories")
                .registerPattern("META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports");
        }

        private void registerSerializationHints(RuntimeHints hints) {
            // Register common serialization classes
            hints.serialization()
                .registerType(TypeReference.of(java.util.concurrent.CompletableFuture.class))
                .registerType(TypeReference.of(java.time.Duration.class))
                .registerType(TypeReference.of(java.time.Instant.class));
        }
    }
}

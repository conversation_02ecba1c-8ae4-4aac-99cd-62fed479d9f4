package com.tui.destilink.framework.core.observability;

import io.micrometer.common.KeyValue;
import io.micrometer.observation.ObservationRegistry;
import io.micrometer.observation.ObservationRegistry.ObservationConfig;
import org.springframework.boot.actuate.autoconfigure.observation.ObservationRegistryCustomizer;

/**
 * Customizer for ObservationRegistry with Destilink Framework-specific configurations.
 * Adds common tags, observation handlers, and optimizations.
 * 
 * @since Spring Boot 3.5.4
 */
public class DestiLinkObservationRegistryCustomizer implements ObservationRegistryCustomizer<ObservationRegistry> {

    private final ObservabilityProperties properties;

    public DestiLinkObservationRegistryCustomizer(ObservabilityProperties properties) {
        this.properties = properties;
    }

    @Override
    public void customize(ObservationRegistry registry) {
        ObservationConfig config = registry.observationConfig();
        
        // Add common tags for all observations
        config.observationFilter(context -> {
            context.addLowCardinalityKeyValue(KeyValue.of("framework", "destilink"));
            context.addLowCardinalityKeyValue(KeyValue.of("framework.version", getFrameworkVersion()));
            return context;
        });

        // Configure high cardinality tags if enabled
        if (properties.getHighCardinalityTags()) {
            config.observationFilter(context -> {
                context.addHighCardinalityKeyValue(KeyValue.of("thread.name", Thread.currentThread().getName()));
                return context;
            });
        }
    }

    private String getFrameworkVersion() {
        // Try to get version from package manifest
        Package pkg = DestiLinkObservationRegistryCustomizer.class.getPackage();
        String version = pkg != null ? pkg.getImplementationVersion() : null;
        return version != null ? version : "unknown";
    }
}

package com.tui.destilink.framework.core.observability;

import io.micrometer.observation.ObservationRegistry;
import io.micrometer.observation.aop.ObservedAspect;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auto-configuration for enhanced observability using Spring Boot 3.5+ ObservationRegistry.
 * Provides structured observability with metrics, traces, and logs integration.
 * 
 * @since Spring Boot 3.5.4
 */
@AutoConfiguration
@EnableConfigurationProperties(ObservabilityProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.core.observability", name = "enabled", 
                      havingValue = "true", matchIfMissing = true)
@ConditionalOnClass(ObservationRegistry.class)
public class ObservabilityAutoConfiguration {

    /**
     * Enhanced ObservationRegistry customizer for framework-specific observations.
     */
    @Bean
    @ConditionalOnMissingBean
    public DestiLinkObservationRegistryCustomizer observationRegistryCustomizer(ObservabilityProperties properties) {
        return new DestiLinkObservationRegistryCustomizer(properties);
    }

    /**
     * Configuration for @Observed aspect support.
     */
    @Configuration
    @ConditionalOnClass(ObservedAspect.class)
    static class ObservedAspectConfiguration {

        @Bean
        @ConditionalOnMissingBean
        public ObservedAspect observedAspect(ObservationRegistry observationRegistry) {
            return new ObservedAspect(observationRegistry);
        }
    }
}

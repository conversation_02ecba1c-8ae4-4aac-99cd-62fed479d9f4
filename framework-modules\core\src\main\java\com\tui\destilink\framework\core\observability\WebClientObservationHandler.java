package com.tui.destilink.framework.core.observability;

import io.micrometer.common.KeyValue;
import io.micrometer.observation.Observation;
import io.micrometer.observation.ObservationHandler;

/**
 * Observation handler for Web Client operations.
 * Adds HTTP client-specific metrics and tracing context.
 * 
 * @since Spring Boot 3.5.4
 */
public class WebClientObservationHandler implements ObservationHandler<Observation.Context> {

    @Override
    public void onStart(Observation.Context context) {
        if (context.getName().startsWith("http.client.")) {
            context.addLowCardinalityKeyValue(KeyValue.of("component", "webclient"));
        }
    }

    @Override
    public void onError(Observation.Context context) {
        if (context.getName().startsWith("http.client.")) {
            context.addLowCardinalityKeyValue(KeyValue.of("http.error", "true"));
        }
    }

    @Override
    public void onEvent(Observation.Event event, Observation.Context context) {
        // Handle HTTP client-specific events
    }

    @Override
    public void onScopeOpened(Observation.Context context) {
        // HTTP client scope opened
    }

    @Override
    public void onScopeClosed(Observation.Context context) {
        // HTTP client scope closed
    }

    @Override
    public void onStop(Observation.Context context) {
        // HTTP client operation completed
    }

    @Override
    public boolean supportsContext(Observation.Context context) {
        return context.getName().startsWith("http.client.");
    }
}

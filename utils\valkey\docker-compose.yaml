services:
  valkey-cluster:
    build:
      context: ./8.0
      pull: true
    pull_policy: build
    restart: unless-stopped
    read_only: true
    security_opt:
      - no-new-privileges:true
    ports:
      - '6378:6378'
    volumes:
      - valkey-cluster:/data
    tmpfs:
      - /tmp
    healthcheck:
      test: ["CMD", "valkey-cli", "-p", "6378", "ping"]
      interval: 2s
      timeout: 2s
      retries: 30
      start_period: 5s

volumes:
  valkey-cluster:

# Destilink Framework: Spring Boot 3.5+ Structured Logging Migration Guide

## Overview

This guide describes the migration from `logstash-logback-encoder` to Spring Boot 3.5+ native structured logging while preserving all existing Destilink Framework logging functionality.

## Migration Benefits

- **Simplified Configuration**: Replace complex logback-spring.xml with simple application properties
- **Reduced Dependencies**: Eliminate external `logstash-logback-encoder` dependency
- **Better Spring Boot Integration**: Leverage native Spring Boot 3.5+ structured logging
- **Preserved Functionality**: Maintain message size limiting and global context injection
- **Performance**: Better integration with Spring Boot lifecycle and configuration

## Migration Phases

### Phase 1: Enable New Structured Logging (Opt-in)

Add to your application configuration:

```yaml
destilink:
  fw:
    core:
      logging:
        structured:
          enabled: true  # Enable new structured logging
          message-size-limit: 500KB  # Same as before
          include-global-context: true  # Preserve global context
          format: "destilink"  # Custom format preserving all features

# Optional: Configure Spring Boot native properties
logging:
  structured:
    ecs:
      service:
        name: "${spring.application.name}"
        version: "${app.version:1.0.0}"
        environment: "${spring.profiles.active:development}"
```

### Phase 2: Gradual Migration

#### Option A: Console Structured + File Traditional
```yaml
logging:
  structured:
    format:
      console: "com.tui.destilink.framework.core.logging.structured.DestilinkStructuredLogFormatter"
      # file: Leave unset for traditional file logging
  file:
    name: "application.log"
```

#### Option B: Full Structured Logging
```yaml
logging:
  structured:
    format:
      console: "com.tui.destilink.framework.core.logging.structured.DestilinkStructuredLogFormatter"
      file: "ecs"  # Use native ECS format for files
  file:
    name: "application.json"
```

### Phase 3: Legacy Support Removal (Future)

When ready to fully migrate:

1. Remove `logstash-logback-encoder` dependency from POMs
2. Replace `logback-spring.xml` with simplified configuration
3. Set `destilink.fw.core.logging.structured.use-native-structured-logging: true`

## Configuration Reference

### Destilink Structured Logging Properties

| Property | Default | Description |
|----------|---------|-------------|
| `destilink.fw.core.logging.structured.enabled` | `false` | Enable structured logging |
| `destilink.fw.core.logging.structured.message-size-limit` | `500KB` | Maximum message size before truncation |
| `destilink.fw.core.logging.structured.include-global-context` | `true` | Include global context in logs |
| `destilink.fw.core.logging.structured.format` | `destilink` | Log format type |
| `destilink.fw.core.logging.structured.use-native-structured-logging` | `true` | Use Spring Boot native features |

### Spring Boot Native Properties

| Property | Description |
|----------|-------------|
| `logging.structured.format.console` | Console structured format |
| `logging.structured.format.file` | File structured format |
| `logging.structured.ecs.service.name` | Service name in ECS format |
| `logging.structured.ecs.service.version` | Service version |
| `logging.structured.ecs.service.environment` | Service environment |

## Supported Formats

### Destilink Format (Recommended)
- Preserves all existing functionality
- Message size limiting
- Global context injection
- ECS-compatible field structure

### ECS Format
- Elastic Common Schema compliance
- Best for Elasticsearch integration
- Service metadata support

### Logstash Format
- Compatible with existing Logstash pipelines
- Drop-in replacement for logstash-logback-encoder

### GELF Format
- Graylog Extended Log Format
- Optimized for Graylog integration

## Global Context API

The enhanced `GlobalContextJsonProvider` provides a public API:

```java
// Add global context (appears in all log events)
GlobalContextJsonProvider.putGlobalContext("correlation.id", "abc-123");
GlobalContextJsonProvider.putGlobalContext("user.tenant", "customer-xyz");

// Access global context
Map<String, String> context = GlobalContextJsonProvider.getGlobalContext();

// Remove specific entries
GlobalContextJsonProvider.removeGlobalContext("correlation.id");

// Clear all global context
GlobalContextJsonProvider.clearGlobalContext();
```

## Log Format Examples

### Destilink Format Output
```json
{
  "@timestamp": "2025-08-11T15:30:00.123Z",
  "log.level": "INFO",
  "process.pid": 12345,
  "process.thread.name": "main",
  "log.logger": "com.tui.destilink.service.OrderService",
  "message": "Processing order for customer",
  "correlation.id": "abc-123",
  "user.tenant": "customer-xyz",
  "order.id": "order-456"
}
```

### With Message Size Limiting
```json
{
  "@timestamp": "2025-08-11T15:30:00.123Z",
  "log.level": "WARN",
  "process.pid": 12345,
  "process.thread.name": "worker-1",
  "log.logger": "com.tui.destilink.service.DataProcessor",
  "message": "Large payload received: {\"customer\":\"...\"}…[Cut 2.1 MB to ≤500.0 KB]"
}
```

## Testing

Test structured logging with different profiles:

```yaml
# application-test.yml
destilink:
  fw:
    core:
      logging:
        structured:
          enabled: true
          message-size-limit: 100KB  # Smaller limit for tests

# Use traditional logging in tests if needed
spring:
  profiles:
    active: test
```

## Monitoring and Troubleshooting

### Enable Debug Logging
```yaml
logging:
  level:
    com.tui.destilink.framework.core.logging.structured: DEBUG
```

### Common Issues

1. **JSON Serialization Errors**: Check for non-serializable objects in MDC
2. **Missing Global Context**: Verify `include-global-context: true`
3. **Message Truncation**: Adjust `message-size-limit` if needed
4. **Performance**: Consider async appenders for high-volume logging

## Rollback Strategy

To rollback to previous logging:

```yaml
destilink:
  fw:
    core:
      logging:
        structured:
          enabled: false  # Disable structured logging
```

The framework will automatically fall back to the existing logback-spring.xml configuration.

## Migration Checklist

- [ ] Enable structured logging in test environment
- [ ] Verify log format and content
- [ ] Test message size limiting functionality
- [ ] Validate global context injection
- [ ] Performance testing with new formatter
- [ ] Update monitoring/alerting systems if needed
- [ ] Deploy to staging environment
- [ ] Full production rollout
- [ ] Remove legacy dependencies (future phase)
